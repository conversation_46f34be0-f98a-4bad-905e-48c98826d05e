#!/usr/bin/env python3
"""
Check if our test user has a UserApproval record
"""

import os
import sys
import django

# Add the backend directory to Python path
backend_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'backend')
sys.path.append(backend_path)

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'yasmeen_ai.settings')
django.setup()

from django.contrib.auth.models import User
from users.models import UserApproval, UserProfile, RoleApplication

def check_user_approval():
    """Check if our test user has a UserApproval record"""
    
    # Get the latest debug user
    latest_user = User.objects.filter(username__startswith='debug_test_').order_by('-id').first()
    
    if not latest_user:
        print("No debug test user found")
        return
    
    print(f"Checking UserApproval for user: {latest_user.username} (ID: {latest_user.id})")
    
    # Check if UserApproval exists
    try:
        user_approval = UserApproval.objects.get(user=latest_user)
        print(f"OK UserApproval found:")
        print(f"  ID: {user_approval.id}")
        print(f"  Status: {user_approval.status}")
        print(f"  Created: {user_approval.created_at}")
        print(f"  Updated: {user_approval.updated_at}")
        print(f"  Reviewed by: {user_approval.reviewed_by}")
        print(f"  Reviewed at: {user_approval.reviewed_at}")
    except UserApproval.DoesNotExist:
        print("ERROR No UserApproval record found")

        # Create one manually for testing
        print("Creating UserApproval record...")
        user_approval = UserApproval.objects.create(
            user=latest_user,
            status='pending'
        )
        print(f"OK Created UserApproval with ID: {user_approval.id}")
    
    # Check all UserApproval records to see what's in the database
    print(f"\n=== All UserApproval Records ===")
    all_approvals = UserApproval.objects.all().order_by('-created_at')
    print(f"Total UserApproval records: {all_approvals.count()}")
    
    for approval in all_approvals[:5]:  # Show first 5
        print(f"  User: {approval.user.username} | Status: {approval.status} | Created: {approval.created_at}")
    
    # Check if our user appears in the serializer output
    print(f"\n=== Testing Serializer Output ===")
    from users.serializers.main import UserApprovalSerializer
    
    try:
        user_approval = UserApproval.objects.get(user=latest_user)
        serializer = UserApprovalSerializer(user_approval)
        data = serializer.data
        
        print(f"Serialized data keys: {list(data.keys())}")
        print(f"User username: {data.get('user_username')}")
        print(f"Status: {data.get('status')}")
        
        # Check motivation and qualifications
        role_info = data.get('requested_role_info', {})
        if role_info:
            print(f"Motivation: {role_info.get('motivation')}")
            print(f"Qualifications: {role_info.get('qualifications')}")
        else:
            print("No requested_role_info found")
            
    except Exception as e:
        print(f"Error testing serializer: {e}")

if __name__ == "__main__":
    check_user_approval()
