"""
Dedicated authentication URLs
These endpoints provide clean auth URLs that match the frontend expectations
"""
from django.urls import path
from rest_framework_simplejwt.views import (
    TokenObtainPairView,
    TokenRefreshView,
    TokenVerifyView,
)
from .auth_views import LoginView, LogoutView, EnhancedRegisterView, UserView, CheckUsernameView, CheckEmailView

from .views.email_verification import (
    EmailVerificationRequestView,
    EmailVerificationConfirmView,
    EmailVerificationStatusView,
    EmailVerificationResendView,
    EmailVerificationSettingsView,
    verify_email_token
)

urlpatterns = [
    # Authentication endpoints that match frontend expectations
    path('login/', LoginView.as_view(), name='auth_login'),
    path('logout/', LogoutView.as_view(), name='auth_logout'),

    path('register-enhanced/', EnhancedRegisterView.as_view(), name='auth_register_enhanced'),
    path('check-username/', CheckUsernameView.as_view(), name='auth_check_username'),
    path('check-email/', CheckEmailView.as_view(), name='auth_check_email'),
    path('user/', UserView.as_view(), name='auth_user'),
    
    # JWT Token endpoints
    path('token/', TokenObtainPairView.as_view(), name='token_obtain_pair'),
    path('refresh/', TokenRefreshView.as_view(), name='token_refresh'),
    path('verify/', TokenVerifyView.as_view(), name='token_verify'),

    # Email Verification endpoints
    path('email/request-verification/', EmailVerificationRequestView.as_view(), name='email_request_verification'),
    path('email/verify/', EmailVerificationConfirmView.as_view(), name='email_verify'),
    path('email/verify/<uuid:token>/', verify_email_token, name='email_verify_token'),
    path('email/status/', EmailVerificationStatusView.as_view(), name='email_verification_status'),
    path('email/resend/', EmailVerificationResendView.as_view(), name='email_resend_verification'),
    path('email/settings/', EmailVerificationSettingsView.as_view(), name='email_verification_settings'),
]
