/**
 * ✅ ICON MAPPING UTILITY
 * Maps string icon names to actual Lucide React components
 */

import React from 'react';
import {
  Home,
  User,
  Settings,
  Bot,
  LogOut,
  X,
  Menu,
  Shield,
  BarChart3,
  Users,
  UserCheck,
  FileText,
  Calendar,
  TrendingUp,
  Briefcase,
  DollarSign,
  BookOpen,
  Plus,
  Search,
  Flag,
  Folder,
  ChevronDown,
  ChevronRight,
  Bell,
  Globe
} from 'lucide-react';

// Icon mapping object
const iconMap = {
  Home,
  User,
  Settings,
  Bot,
  LogOut,
  X,
  Menu,
  Shield,
  BarChart3,
  Users,
  UserCheck,
  FileText,
  Calendar,
  TrendingUp,
  Briefcase,
  DollarSign,
  BookOpen,
  Plus,
  Search,
  Flag,
  Folder,
  ChevronDown,
  ChevronRight,
  Bell,
  Globe
} as const;

export type IconName = keyof typeof iconMap;

/**
 * Get icon component by name
 */
export const getIcon = (iconName: string): React.ComponentType<any> => {
  const IconComponent = iconMap[iconName as IconName];
  return IconComponent || Home; // Fallback to Home icon
};

/**
 * Render icon component with props
 */
export const renderIcon = (iconName: string, props: any = {}) => {
  const IconComponent = getIcon(iconName);
  return <IconComponent {...props} />;
};

/**
 * Check if icon name exists
 */
export const isValidIcon = (iconName: string): boolean => {
  return iconName in iconMap;
};

/**
 * Get all available icon names
 */
export const getAvailableIcons = (): string[] => {
  return Object.keys(iconMap);
};

export default iconMap;
