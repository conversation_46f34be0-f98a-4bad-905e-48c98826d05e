from django.db import models
from django.contrib.auth.models import User
from django.db.models.signals import post_save
from django.dispatch import receiver
from django.core.validators import MinValueValidator, MaxValueValidator
from django.utils import timezone
from django.core.mail import send_mail
from django.conf import settings
from api.storage import OptimizedImageStorage


class UserRole(models.Model):
    """Defines available roles in the system with their permissions"""

    ROLE_CHOICES = (
        ('super_admin', 'Super Administrator'),
        ('admin', 'Administrator'),
        ('moderator', 'Moderator'),
        ('entrepreneur', 'Entrepreneur'),
        ('mentor', 'Mentor'),
        ('investor', 'Investor'),
        ('user', 'Regular User'),
    )

    PERMISSION_CHOICES = (
        ('read', 'Read Only'),
        ('write', 'Read & Write'),
        ('moderate', 'Moderate Content'),
        ('admin', 'Full Admin Access'),
        ('super_admin', 'Super Admin System Control'),
    )

    name = models.CharField(max_length=50, choices=ROLE_CHOICES, unique=True)
    display_name = models.CharField(max_length=100)
    description = models.TextField(blank=True)
    permission_level = models.CharField(max_length=20, choices=PERMISSION_CHOICES, default='read')
    is_active = models.BooleanField(default=True)
    requires_approval = models.BooleanField(default=True, help_text="Whether this role requires admin approval")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.display_name

    class Meta:
        ordering = ['name']
        verbose_name = 'User Role'
        verbose_name_plural = 'User Roles'


class RoleApplication(models.Model):
    """Applications for specific roles submitted by users"""

    STATUS_CHOICES = (
        ('pending', 'Pending Review'),
        ('approved', 'Approved'),
        ('rejected', 'Rejected'),
        ('withdrawn', 'Withdrawn'),
    )

    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='role_applications')
    requested_role = models.ForeignKey(UserRole, on_delete=models.CASCADE)
    motivation = models.TextField(help_text="Why do you want this role?")
    qualifications = models.TextField(help_text="What qualifies you for this role?")
    experience = models.TextField(blank=True, help_text="Relevant experience")
    portfolio_url = models.URLField(blank=True, help_text="Portfolio or work samples")
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    reviewed_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='role_reviews_made')
    reviewed_at = models.DateTimeField(null=True, blank=True)
    admin_notes = models.TextField(blank=True, help_text="Internal notes for admins")

    # Entrepreneur/User specific fields
    company_name = models.CharField(max_length=200, blank=True, help_text="Company or project name")
    project_stage = models.CharField(max_length=50, blank=True, choices=[
        ('idea', 'Just an Idea'),
        ('planning', 'Planning Stage'),
        ('prototype', 'Prototype'),
        ('mvp', 'MVP (Minimum Viable Product)'),
        ('launched', 'Launched'),
        ('scaling', 'Scaling'),
    ], help_text="Current stage of the project")
    industry = models.CharField(max_length=100, blank=True, help_text="Industry or sector")
    project_description = models.TextField(blank=True, help_text="Detailed description of the project/idea")
    funding_needed = models.CharField(max_length=50, blank=True, choices=[
        ('0-10k', 'Less than $10,000'),
        ('10k-50k', '$10,000 - $50,000'),
        ('50k-100k', '$50,000 - $100,000'),
        ('100k-500k', '$100,000 - $500,000'),
        ('500k-1m', '$500,000 - $1,000,000'),
        ('1m+', 'More than $1,000,000'),
        ('no-funding', 'No funding needed currently'),
    ], help_text="Amount of funding needed")
    team_size = models.CharField(max_length=20, blank=True, choices=[
        ('solo', 'Solo Founder'),
        ('2-3', '2-3 People'),
        ('4-10', '4-10 People'),
        ('11-25', '11-25 People'),
        ('25+', 'More than 25 People'),
    ], help_text="Current team size")
    support_needed = models.JSONField(default=list, blank=True, help_text="Types of support needed (JSON array)")
    previous_experience = models.TextField(blank=True, help_text="Previous entrepreneurship experience")

    # Mentor specific fields
    expertise_areas = models.JSONField(default=list, blank=True, help_text="Areas of expertise (JSON array)")
    mentoring_experience = models.TextField(blank=True, help_text="Previous mentoring experience")
    availability = models.CharField(max_length=50, blank=True, choices=[
        ('1-2h/week', '1-2 hours per week'),
        ('3-5h/week', '3-5 hours per week'),
        ('5-10h/week', '5-10 hours per week'),
        ('10+h/week', 'More than 10 hours per week'),
        ('flexible', 'Flexible schedule'),
    ], help_text="Time availability for mentoring")
    preferred_communication = models.JSONField(default=list, blank=True, help_text="Preferred communication methods")

    # Investor specific fields
    investment_range = models.CharField(max_length=50, blank=True, choices=[
        ('1k-10k', '$1,000 - $10,000'),
        ('10k-50k', '$10,000 - $50,000'),
        ('50k-100k', '$50,000 - $100,000'),
        ('100k-500k', '$100,000 - $500,000'),
        ('500k-1m', '$500,000 - $1,000,000'),
        ('1m-5m', '$1,000,000 - $5,000,000'),
        ('5m+', 'More than $5,000,000'),
    ], help_text="Typical investment range")
    investment_focus = models.JSONField(default=list, blank=True, help_text="Investment focus areas (JSON array)")
    investment_stage = models.JSONField(default=list, blank=True, help_text="Preferred investment stages")
    portfolio_companies = models.TextField(blank=True, help_text="Previous investments or portfolio companies")
    due_diligence_requirements = models.TextField(blank=True, help_text="Due diligence requirements")

    # Community Member specific fields
    interests = models.TextField(blank=True, help_text="Areas of interest")
    goals = models.TextField(blank=True, help_text="Personal or professional goals")

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.user.username} - {self.requested_role.display_name} ({self.status})"

    def approve(self, admin_user):
        """Approve the role application and create role assignment"""
        from django.utils import timezone

        self.status = 'approved'
        self.reviewed_by = admin_user
        self.reviewed_at = timezone.now()
        self.save()

        # Create role assignment
        try:
            profile = self.user.profile
            assignment, created = UserRoleAssignment.objects.get_or_create(
                user_profile=profile,
                role=self.requested_role,
                defaults={
                    'is_active': True,
                    'is_approved': True,
                    'assigned_by': admin_user,
                    'approved_by': admin_user,
                    'notes': f'Role approved from application on {timezone.now().strftime("%Y-%m-%d")}'
                }
            )

            if not created:
                # Update existing assignment to be active and approved
                assignment.is_active = True
                assignment.is_approved = True
                assignment.assigned_by = admin_user
                assignment.approved_by = admin_user
                assignment.save()

        except UserProfile.DoesNotExist:
            # Create profile if it doesn't exist
            profile = UserProfile.objects.create(user=self.user)
            UserRoleAssignment.objects.create(
                user_profile=profile,
                role=self.requested_role,
                is_active=True,
                is_approved=True,
                assigned_by=admin_user,
                approved_by=admin_user,
                notes=f'Role approved from application on {timezone.now().strftime("%Y-%m-%d")}'
            )

    def reject(self, admin_user, reason=""):
        """Reject the role application"""
        from django.utils import timezone

        self.status = 'rejected'
        self.reviewed_by = admin_user
        self.reviewed_at = timezone.now()
        self.admin_notes = reason
        self.save()

    class Meta:
        ordering = ['-created_at']
        verbose_name = 'Role Application'
        verbose_name_plural = 'Role Applications'
        unique_together = ['user', 'requested_role']


class UserRoleAssignment(models.Model):
    """Tracks role assignments for users"""

    user_profile = models.ForeignKey('UserProfile', on_delete=models.CASCADE)
    role = models.ForeignKey(UserRole, on_delete=models.CASCADE)
    assigned_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='role_assignments_made')
    assigned_at = models.DateTimeField(auto_now_add=True)
    is_active = models.BooleanField(default=True)
    is_approved = models.BooleanField(default=True)
    approved_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='role_approvals_made')
    approved_at = models.DateTimeField(null=True, blank=True)
    expires_at = models.DateTimeField(null=True, blank=True, help_text="Optional expiration date for temporary roles")
    notes = models.TextField(blank=True, help_text="Notes about this role assignment")

    def __str__(self):
        return f"{self.user_profile.user.username} - {self.role.display_name}"

    class Meta:
        ordering = ['-assigned_at']
        verbose_name = 'User Role Assignment'
        verbose_name_plural = 'User Role Assignments'
        unique_together = ['user_profile', 'role']


class UserProfile(models.Model):
    """Extended user profile with additional information"""
    
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='profile')
    bio = models.TextField(max_length=500, blank=True)
    location = models.CharField(max_length=100, blank=True)
    birth_date = models.DateField(null=True, blank=True)
    phone_number = models.CharField(max_length=20, blank=True)
    website = models.URLField(blank=True)
    linkedin_url = models.URLField(blank=True)
    twitter_url = models.URLField(blank=True)
    github_url = models.URLField(blank=True)
    
    # Profile image with optimized storage
    profile_image = models.ImageField(
        upload_to='profile_images/',
        storage=OptimizedImageStorage(),
        blank=True,
        null=True,
        help_text="Profile picture (will be optimized automatically)"
    )
    
    # Professional information
    company = models.CharField(max_length=200, blank=True)
    job_title = models.CharField(max_length=100, blank=True)
    industry = models.CharField(max_length=100, blank=True)
    experience_years = models.PositiveIntegerField(null=True, blank=True, validators=[MaxValueValidator(50)])
    expertise = models.TextField(blank=True, null=True, default='', help_text="Areas of expertise and skills")
    
    # Preferences and settings
    is_active = models.BooleanField(default=True)
    email_notifications = models.BooleanField(default=True)
    marketing_emails = models.BooleanField(default=False)
    profile_visibility = models.CharField(max_length=20, choices=[
        ('public', 'Public'),
        ('members', 'Members Only'),
        ('private', 'Private'),
    ], default='members')
    
    # Language preference
    language = models.CharField(max_length=10, choices=[
        ('en', 'English'),
        ('ar', 'Arabic'),
    ], default='en')

    # Role-specific additional information
    role_additional_info = models.JSONField(default=dict, blank=True, help_text="Additional information specific to user's role")
    requested_role_name = models.CharField(max_length=50, blank=True, help_text="Requested role name for manual review if role not found")

    # Entrepreneur-specific fields
    business_name = models.CharField(max_length=200, blank=True, null=True, help_text="Name of the business or startup")
    business_stage = models.CharField(max_length=50, blank=True, null=True, choices=[
        ('idea', 'Idea'),
        ('prototype', 'Prototype'),
        ('mvp', 'MVP'),
        ('growth', 'Growth'),
        ('established', 'Established'),
    ], help_text="Current stage of the business")
    funding_needed = models.CharField(max_length=50, blank=True, null=True, choices=[
        ('none', 'No funding needed'),
        ('under-50k', 'Under $50K'),
        ('50k-250k', '$50K - $250K'),
        ('250k-1m', '$250K - $1M'),
        ('over-1m', 'Over $1M'),
    ], help_text="Amount of funding needed")
    business_description = models.TextField(max_length=1000, blank=True, null=True, help_text="Description of the business or startup idea")
    team_size = models.CharField(max_length=50, blank=True, null=True, choices=[
        ('1', 'Just me'),
        ('2-5', '2-5 people'),
        ('6-10', '6-10 people'),
        ('11-20', '11-20 people'),
        ('20+', '20+ people'),
    ], help_text="Current team size")
    support_needed = models.TextField(max_length=1000, blank=True, null=True, help_text="Type of support needed")
    previous_experience = models.TextField(max_length=1000, blank=True, null=True, help_text="Previous entrepreneurial experience")

    # Mentor-specific fields
    expertise = models.CharField(max_length=200, blank=True, null=True, help_text="Areas of expertise")
    mentor_experience = models.CharField(max_length=50, blank=True, null=True, choices=[
        ('1-3', '1-3 years'),
        ('4-7', '4-7 years'),
        ('8-15', '8-15 years'),
        ('15+', '15+ years'),
    ], help_text="Years of professional experience for mentoring")
    mentorship_areas = models.TextField(max_length=1000, blank=True, null=True, help_text="Areas where mentor can provide guidance")
    availability = models.CharField(max_length=50, blank=True, null=True, choices=[
        ('1-2-hours', '1-2 hours per week'),
        ('3-5-hours', '3-5 hours per week'),
        ('5+-hours', '5+ hours per week'),
        ('flexible', 'Flexible'),
    ], help_text="Time availability for mentoring")
    preferred_communication = models.CharField(max_length=100, blank=True, null=True, choices=[
        ('email', 'Email'),
        ('video-call', 'Video Call'),
        ('phone', 'Phone'),
        ('chat', 'Chat/Messaging'),
        ('in-person', 'In-person'),
    ], help_text="Preferred communication method")

    # Investor-specific fields
    investment_range = models.CharField(max_length=50, blank=True, null=True, choices=[
        ('10k-50k', '$10K - $50K'),
        ('50k-250k', '$50K - $250K'),
        ('250k-1m', '$250K - $1M'),
        ('1m-5m', '$1M - $5M'),
        ('over-5m', 'Over $5M'),
    ], help_text="Typical investment range")
    investment_stage = models.CharField(max_length=50, blank=True, null=True, choices=[
        ('pre-seed', 'Pre-seed'),
        ('seed', 'Seed'),
        ('series-a', 'Series A'),
        ('series-b', 'Series B'),
        ('growth', 'Growth'),
    ], help_text="Preferred investment stage")
    preferred_industries = models.TextField(max_length=1000, blank=True, null=True, help_text="Industries of interest for investment")
    investment_criteria = models.TextField(max_length=1000, blank=True, null=True, help_text="Investment criteria and requirements")
    portfolio_companies = models.TextField(max_length=1000, blank=True, null=True, help_text="Previous investments or portfolio companies")

    # Community Member/User-specific fields
    interests = models.TextField(max_length=1000, blank=True, null=True, help_text="Areas of interest in entrepreneurship")
    goals = models.TextField(max_length=1000, blank=True, null=True, help_text="Goals and objectives on the platform")

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    last_activity = models.DateTimeField(default=timezone.now)

    def __str__(self):
        return f"{self.user.username}'s Profile"

    class Meta:
        verbose_name = 'User Profile'
        verbose_name_plural = 'User Profiles'


@receiver(post_save, sender=User)
def create_user_profile(sender, instance, created, **kwargs):
    """Create a UserProfile when a new User is created"""
    if created:
        # Use get_or_create to prevent duplicate profile creation
        UserProfile.objects.get_or_create(
            user=instance,
            defaults={
                'language': 'en',
                'is_active': True
            }
        )


@receiver(post_save, sender=User)
def save_user_profile(sender, instance, **kwargs):
    """Save the UserProfile when the User is saved"""
    if hasattr(instance, 'profile'):
        instance.profile.save()


class UserApproval(models.Model):
    """Track user approval status and admin decisions"""

    STATUS_CHOICES = (
        ('pending', 'Pending Review'),
        ('approved', 'Approved'),
        ('rejected', 'Rejected'),
    )

    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='approval_status')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    reviewed_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='user_approvals_made')
    reviewed_at = models.DateTimeField(null=True, blank=True)
    rejection_reason = models.TextField(blank=True, help_text="Reason for rejection (sent to user via email)")
    admin_notes = models.TextField(blank=True, help_text="Internal notes for admins")

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.user.username} - {self.get_status_display()}"

    def approve(self, admin_user):
        """Approve the user and activate their account"""
        self.status = 'approved'
        self.reviewed_by = admin_user
        self.reviewed_at = timezone.now()
        self.save()

        # Activate the user account
        self.user.is_active = True
        self.user.save()

        # Check for pending role applications and approve them
        pending_applications = RoleApplication.objects.filter(
            user=self.user,
            status='pending'
        )

        for application in pending_applications:
            application.approve(admin_user)

        # Send approval email
        self.send_approval_email()

    def reject(self, admin_user, reason=""):
        """Reject the user with optional reason"""
        self.status = 'rejected'
        self.reviewed_by = admin_user
        self.reviewed_at = timezone.now()
        self.rejection_reason = reason
        self.save()

        # Send rejection email
        self.send_rejection_email()

    def send_approval_email(self):
        """Send approval notification email to user"""
        try:
            subject = 'Your Account Has Been Approved - Yasmeen AI'
            message = f"""
Dear {self.user.first_name or self.user.username},

Great news! Your account registration has been approved by our admin team.

You can now log in to your account and start using all the features of Yasmeen AI platform.

Login here: {settings.FRONTEND_URL}/login

Welcome to the Yasmeen AI community!

Best regards,
The Yasmeen AI Team
            """.strip()

            send_mail(
                subject=subject,
                message=message,
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=[self.user.email],
                fail_silently=False,
            )
        except Exception as e:
            print(f"Failed to send approval email to {self.user.email}: {e}")

    def send_rejection_email(self):
        """Send rejection notification email to user"""
        try:
            subject = 'Account Registration Update - Yasmeen AI'
            message = f"""
Dear {self.user.first_name or self.user.username},

Thank you for your interest in joining Yasmeen AI platform.

After reviewing your application, we are unable to approve your account at this time.

{f"Reason: {self.rejection_reason}" if self.rejection_reason else ""}

If you have any questions or would like to reapply in the future, please contact our support team.

Best regards,
The Yasmeen AI Team
            """.strip()

            send_mail(
                subject=subject,
                message=message,
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=[self.user.email],
                fail_silently=False,
            )
        except Exception as e:
            print(f"Failed to send rejection email to {self.user.email}: {e}")

    class Meta:
        verbose_name = 'User Approval'
        verbose_name_plural = 'User Approvals'


@receiver(post_save, sender=User)
def create_user_approval(sender, instance, created, **kwargs):
    """Create UserApproval when a new User is created"""
    if created and not instance.is_superuser and not instance.is_staff:
        # Only create approval for regular users, not admin/staff accounts
        # Use get_or_create to prevent duplicate approval creation
        UserApproval.objects.get_or_create(
            user=instance,
            defaults={
                'status': 'pending'
            }
        )
