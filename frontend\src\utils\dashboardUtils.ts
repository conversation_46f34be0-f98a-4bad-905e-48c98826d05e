/**
 * Dashboard Utilities
 * Helper functions for dashboard components
 */

import { DashboardRole } from '../types/dashboard';

/**
 * Get role-specific color scheme
 */
export const getRoleColorScheme = (role: DashboardRole) => {
  const colorSchemes = {
    user: {
      primary: 'indigo',
      secondary: 'purple',
      accent: 'blue'
    },
    entrepreneur: {
      primary: 'orange',
      secondary: 'red',
      accent: 'yellow'
    },
    admin: {
      primary: 'purple',
      secondary: 'violet',
      accent: 'indigo'
    },
    super_admin: {
      primary: 'red',
      secondary: 'purple',
      accent: 'pink'
    },
    mentor: {
      primary: 'blue',
      secondary: 'indigo',
      accent: 'cyan'
    },
    investor: {
      primary: 'green',
      secondary: 'emerald',
      accent: 'teal'
    },
    moderator: {
      primary: 'gray',
      secondary: 'slate',
      accent: 'zinc'
    }
  };

  return colorSchemes[role] || colorSchemes.user;
};

/**
 * Format numbers for display
 */
export const formatNumber = (num: number): string => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M';
  }
  if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K';
  }
  return num.toString();
};

/**
 * Get role display name
 */
export const getRoleDisplayName = (role: DashboardRole, isRTL: boolean = false): string => {
  const roleNames = {
    user: { en: 'User', ar: 'مستخدم' },
    entrepreneur: { en: 'Entrepreneur', ar: 'رائد أعمال' },
    admin: { en: 'Admin', ar: 'مدير' },
    super_admin: { en: 'Super Admin', ar: 'مدير عام' },
    mentor: { en: 'Mentor', ar: 'موجه' },
    investor: { en: 'Investor', ar: 'مستثمر' },
    moderator: { en: 'Moderator', ar: 'مشرف' }
  };

  return isRTL ? roleNames[role]?.ar || roleNames.user.ar : roleNames[role]?.en || roleNames.user.en;
};

/**
 * Check if role has admin privileges
 */
export const hasAdminPrivileges = (role: DashboardRole): boolean => {
  return ['admin', 'super_admin'].includes(role);
};

/**
 * Check if role has system access
 */
export const hasSystemAccess = (role: DashboardRole): boolean => {
  return role === 'super_admin';
};

/**
 * Get default dashboard route for role
 */
export const getDashboardRouteForRole = (role: DashboardRole): string => {
  return `/${role}/dashboard`;
};

/**
 * Format stat values for display
 */
export const formatStatValue = (value: string | number, type: 'number' | 'currency' | 'percentage' = 'number'): string => {
  if (typeof value === 'string') {
    return value;
  }

  switch (type) {
    case 'currency':
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0,
      }).format(value);
    case 'percentage':
      return `${value.toFixed(1)}%`;
    case 'number':
    default:
      return formatNumber(value);
  }
};

/**
 * Get change indicator for stats
 */
export const getChangeIndicator = (change: number): { color: string; icon: string; text: string } => {
  if (change > 0) {
    return {
      color: 'text-green-400',
      icon: 'trending-up',
      text: `+${change.toFixed(1)}%`
    };
  } else if (change < 0) {
    return {
      color: 'text-red-400',
      icon: 'trending-down',
      text: `${change.toFixed(1)}%`
    };
  } else {
    return {
      color: 'text-gray-400',
      icon: 'minus',
      text: '0%'
    };
  }
};
