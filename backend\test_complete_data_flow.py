from django.contrib.auth.models import User
from users.models import UserProfile, RoleApplication
from users.serializers.main import UserApprovalSerializer

def test_complete_data_flow():
    """Test the complete data flow from registration to super admin interface"""
    print("Testing Complete Data Flow...")
    print("=" * 50)
    
    # Find the latest investor user
    latest_user = User.objects.order_by('-date_joined').first()
    
    if not latest_user:
        print("No users found")
        return False
    
    print(f"Testing user: {latest_user.username}")
    print(f"Email: {latest_user.email}")
    
    # Check UserProfile data
    print("\n1. UserProfile Data:")
    if hasattr(latest_user, 'profile'):
        profile = latest_user.profile
        print(f"   Location: {profile.location}")
        print(f"   Company: {profile.company}")
        print(f"   Job Title: {profile.job_title}")
        print(f"   Phone: {profile.phone_number}")
        print(f"   Bio: {profile.bio}")
        print(f"   Investment Range: {getattr(profile, 'investment_range', 'Not found')}")
        print(f"   Investment Stage: {getattr(profile, 'investment_stage', 'Not found')}")
        print(f"   Preferred Industries: {getattr(profile, 'preferred_industries', 'Not found')}")
        print(f"   Investment Criteria: {getattr(profile, 'investment_criteria', 'Not found')}")
        print(f"   Portfolio Companies: {getattr(profile, 'portfolio_companies', 'Not found')}")
        print(f"   Team Size: {getattr(profile, 'team_size', 'Not found')}")
        print(f"   Support Needed: {getattr(profile, 'support_needed', 'Not found')}")
        print(f"   Previous Experience: {getattr(profile, 'previous_experience', 'Not found')}")
        print(f"   Preferred Communication: {getattr(profile, 'preferred_communication', 'Not found')}")
    else:
        print("   No profile found")
        return False
    
    # Check RoleApplication data
    print("\n2. RoleApplication Data:")
    role_app = RoleApplication.objects.filter(user=latest_user).first()
    if role_app:
        print(f"   Requested Role: {role_app.requested_role}")
        print(f"   Status: {role_app.status}")
        print(f"   Investment Range: {getattr(role_app, 'investment_range', 'Not found')}")
        print(f"   Investment Focus: {getattr(role_app, 'investment_focus', 'Not found')}")
        print(f"   Investment Stage: {getattr(role_app, 'investment_stage', 'Not found')}")
        print(f"   Portfolio Companies: {getattr(role_app, 'portfolio_companies', 'Not found')}")
        print(f"   Due Diligence Requirements: {getattr(role_app, 'due_diligence_requirements', 'Not found')}")
        print(f"   Experience: {getattr(role_app, 'experience', 'Not found')}")
        print(f"   Portfolio URL: {getattr(role_app, 'portfolio_url', 'Not found')}")
    else:
        print("   No role application found")
        return False
    
    # Test UserApprovalSerializer (what super admin sees)
    print("\n3. Super Admin Interface Data (UserApprovalSerializer):")
    try:
        serializer = UserApprovalSerializer(role_app)
        data = serializer.data
        
        print(f"   User Username: {data.get('user_username', 'Not found')}")
        print(f"   User Email: {data.get('user_email', 'Not found')}")
        print(f"   Status: {data.get('status', 'Not found')}")
        
        # Check profile summary
        profile_summary = data.get('profile_summary', {})
        print(f"   Profile Summary:")
        print(f"     Location: {profile_summary.get('location', 'Not found')}")
        print(f"     Company: {profile_summary.get('company', 'Not found')}")
        print(f"     Job Title: {profile_summary.get('job_title', 'Not found')}")
        print(f"     Phone: {profile_summary.get('phone_number', 'Not found')}")
        print(f"     Bio: {profile_summary.get('bio', 'Not found')}")
        
        # Check role-specific data
        role_specific = data.get('role_specific_data', {})
        print(f"   Role-Specific Data:")
        print(f"     Investment Range: {role_specific.get('investment_range', 'Not found')}")
        print(f"     Investment Stage: {role_specific.get('investment_stage', 'Not found')}")
        print(f"     Preferred Industries: {role_specific.get('preferred_industries', 'Not found')}")
        print(f"     Investment Criteria: {role_specific.get('investment_criteria', 'Not found')}")
        print(f"     Portfolio Companies: {role_specific.get('portfolio_companies', 'Not found')}")
        print(f"     Experience: {role_specific.get('experience', 'Not found')}")
        print(f"     Portfolio URL: {role_specific.get('portfolio_url', 'Not found')}")
        
        # Check requested role info
        role_info = data.get('requested_role_info', {})
        print(f"   Requested Role Info:")
        print(f"     Role Name: {role_info.get('role_name', 'Not found')}")
        print(f"     Display Name: {role_info.get('display_name', 'Not found')}")
        
    except Exception as e:
        print(f"   Error with UserApprovalSerializer: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    print("\n4. Data Consistency Check:")
    issues = []
    
    # Check if data from registration matches what super admin sees
    if profile.portfolio_companies != role_specific.get('portfolio_companies'):
        issues.append(f"Portfolio companies mismatch: Profile='{profile.portfolio_companies}', SuperAdmin='{role_specific.get('portfolio_companies')}'")
    
    if profile.investment_range != role_specific.get('investment_range'):
        issues.append(f"Investment range mismatch: Profile='{profile.investment_range}', SuperAdmin='{role_specific.get('investment_range')}'")
    
    if profile.investment_criteria != role_specific.get('investment_criteria'):
        issues.append(f"Investment criteria mismatch: Profile='{profile.investment_criteria}', SuperAdmin='{role_specific.get('investment_criteria')}'")
    
    if issues:
        print("   Issues found:")
        for issue in issues:
            print(f"     - {issue}")
        return False
    else:
        print("   All data is consistent!")
        return True

if __name__ == "__main__":
    success = test_complete_data_flow()
    print("=" * 50)
    if success:
        print("SUCCESS: Complete data flow is working correctly!")
    else:
        print("FAILURE: There are issues with the data flow!")
