import { test, expect } from '@playwright/test';

test('basic test - app loads', async ({ page }) => {
  console.log('🧪 Running basic test...');

  await page.goto('/');

  // Check if the page loads (look for any content)
  await expect(page.locator('body')).toBeVisible();

  // Check if we can see some content
  const pageContent = await page.textContent('body');
  expect(pageContent).toBeTruthy();

  console.log('✅ Basic test passed! Page loaded successfully');
});
