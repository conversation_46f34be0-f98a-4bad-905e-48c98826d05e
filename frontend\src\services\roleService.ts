/**
 * ROLE SERVICE - Real API Integration
 * Fetches user roles and permissions from backend API
 */

import { apiRequest } from './api';
import { roleApi } from './roleApi';

// ===== TYPES =====
export interface UserRole {
  id: number;
  name: string;
  display_name: string;
  description: string;
  permission_level: string;
  requires_approval: boolean;
  is_active: boolean;
}

export interface UserWithRoles {
  id: number;
  username: string;
  email: string;
  first_name: string;
  last_name: string;
  role: string;
  roles: UserRole[];
  is_staff: boolean;
  is_superuser: boolean;
  is_active: boolean;
  date_joined: string;
}

export interface NavigationItem {
  id: string;
  label: string;
  path: string;
  icon: string;
  requiresAuth: boolean;
  roles?: string[];
}

// ===== ROLE SERVICE CLASS =====
class RoleService {
  private userRoles: UserRole[] = [];
  private currentUser: UserWithRoles | null = null;

  /**
   * Initialize role service with user data
   */
  async initialize(user: any): Promise<void> {
    try {
      // Fetch user roles from API
      const userRoles = await this.fetchUserRoles(user.id);
      this.userRoles = userRoles;
      this.currentUser = {
        ...user,
        roles: userRoles
      };
    } catch (error) {
      console.error('Failed to initialize role service:', error);
      // Fallback to basic role detection
      this.currentUser = {
        ...user,
        roles: []
      };
    }
  }

  /**
   * Fetch user roles from backend API
   */
  private async fetchUserRoles(userId: number): Promise<UserRole[]> {
    try {
      const response = await apiRequest(`/users/${userId}/roles/`, 'GET');
      return response.roles || [];
    } catch (error) {
      console.error('Error fetching user roles:', error);
      return [];
    }
  }

  /**
   * Get primary role for the current user
   */
  getPrimaryRole(): string {
    if (!this.currentUser) return 'user';

    // Check if user is superuser
    if (this.currentUser.is_superuser) return 'super_admin';
    
    // Check if user is staff/admin
    if (this.currentUser.is_staff) return 'admin';

    // Check user's assigned role
    if (this.currentUser.role) return this.currentUser.role;

    // Check roles array for highest permission level
    if (this.userRoles.length > 0) {
      const roleHierarchy = ['super_admin', 'admin', 'moderator', 'mentor', 'investor', 'entrepreneur', 'user'];
      for (const roleType of roleHierarchy) {
        if (this.userRoles.some(r => r.name === roleType)) {
          return roleType;
        }
      }
    }

    return 'user'; // Default fallback
  }

  /**
   * Check if user has specific role
   */
  hasRole(roleName: string): boolean {
    if (!this.currentUser) return false;

    // Check primary role
    if (this.getPrimaryRole() === roleName) return true;

    // Check roles array
    return this.userRoles.some(role => role.name === roleName);
  }

  /**
   * Check if user has any of the specified roles
   */
  hasAnyRole(roleNames: string[]): boolean {
    return roleNames.some(role => this.hasRole(role));
  }

  /**
   * Get sidebar navigation based on user role
   */
  getSidebarNavigation(): NavigationItem[] {
    const primaryRole = this.getPrimaryRole();
    
    switch (primaryRole) {
      case 'super_admin':
        return this.getSuperAdminNavigation();
      case 'admin':
        return this.getAdminNavigation();
      case 'moderator':
        return this.getModeratorNavigation();
      case 'mentor':
        return this.getMentorNavigation();
      case 'investor':
        return this.getInvestorNavigation();
      case 'entrepreneur':
        return this.getEntrepreneurNavigation();
      default:
        return this.getUserNavigation();
    }
  }

  /**
   * Super Admin Navigation
   */
  private getSuperAdminNavigation(): NavigationItem[] {
    return [
      { id: 'dashboard', label: 'لوحة التحكم', path: '/super-admin/dashboard', icon: 'LayoutDashboard', requiresAuth: true },
      { id: 'system', label: 'إدارة النظام', path: '/super-admin/system', icon: 'Settings', requiresAuth: true },
      { id: 'users', label: 'إدارة المستخدمين', path: '/super-admin/users', icon: 'Users', requiresAuth: true },
      { id: 'analytics', label: 'التحليلات المتقدمة', path: '/super-admin/analytics', icon: 'BarChart3', requiresAuth: true },
      { id: 'ai-chat', label: 'محادثة الذكاء الاصطناعي', path: '/super-admin/ai-chat', icon: 'MessageSquare', requiresAuth: true }
    ];
  }

  /**
   * Admin Navigation
   */
  private getAdminNavigation(): NavigationItem[] {
    return [
      { id: 'dashboard', label: 'لوحة التحكم', path: '/admin/dashboard', icon: 'LayoutDashboard', requiresAuth: true },
      { id: 'ai-chat', label: 'محادثة الذكاء الاصطناعي', path: '/admin/ai-chat', icon: 'MessageSquare', requiresAuth: true },
      { id: 'users', label: 'إدارة المستخدمين', path: '/admin/users', icon: 'Users', requiresAuth: true },
      { id: 'content', label: 'إدارة المحتوى', path: '/admin/content', icon: 'FileText', requiresAuth: true },
      { id: 'analytics', label: 'التحليلات', path: '/admin/analytics', icon: 'BarChart3', requiresAuth: true }
    ];
  }

  /**
   * Moderator Navigation
   */
  private getModeratorNavigation(): NavigationItem[] {
    return [
      { id: 'dashboard', label: 'لوحة التحكم', path: '/moderator/dashboard', icon: 'LayoutDashboard', requiresAuth: true },
      { id: 'ai-chat', label: 'محادثة الذكاء الاصطناعي', path: '/moderator/ai-chat', icon: 'MessageSquare', requiresAuth: true },
      { id: 'content', label: 'مراجعة المحتوى', path: '/moderator/content', icon: 'FileText', requiresAuth: true },
      { id: 'reports', label: 'التقارير', path: '/moderator/reports', icon: 'Flag', requiresAuth: true }
    ];
  }

  /**
   * Mentor Navigation
   */
  private getMentorNavigation(): NavigationItem[] {
    return [
      { id: 'dashboard', label: 'لوحة التحكم', path: '/mentor/dashboard', icon: 'LayoutDashboard', requiresAuth: true },
      { id: 'ai-chat', label: 'محادثة الذكاء الاصطناعي', path: '/mentor/ai-chat', icon: 'MessageSquare', requiresAuth: true },
      { id: 'mentorship', label: 'جلسات الإرشاد', path: '/mentor/mentorship', icon: 'Users', requiresAuth: true },
      { id: 'students', label: 'المتدربون', path: '/mentor/students', icon: 'GraduationCap', requiresAuth: true },
      { id: 'schedule', label: 'الجدولة', path: '/mentor/schedule', icon: 'Calendar', requiresAuth: true }
    ];
  }

  /**
   * Investor Navigation
   */
  private getInvestorNavigation(): NavigationItem[] {
    return [
      { id: 'dashboard', label: 'لوحة التحكم', path: '/investor/dashboard', icon: 'LayoutDashboard', requiresAuth: true },
      { id: 'ai-chat', label: 'محادثة الذكاء الاصطناعي', path: '/investor/ai-chat', icon: 'MessageSquare', requiresAuth: true },
      { id: 'opportunities', label: 'الفرص الاستثمارية', path: '/investor/opportunities', icon: 'TrendingUp', requiresAuth: true },
      { id: 'portfolio', label: 'المحفظة', path: '/investor/portfolio', icon: 'Briefcase', requiresAuth: true },
      { id: 'due-diligence', label: 'العناية الواجبة', path: '/investor/due-diligence', icon: 'Search', requiresAuth: true }
    ];
  }

  /**
   * Entrepreneur Navigation
   */
  private getEntrepreneurNavigation(): NavigationItem[] {
    return [
      { id: 'dashboard', label: 'لوحة التحكم', path: '/entrepreneur/dashboard', icon: 'LayoutDashboard', requiresAuth: true },
      { id: 'ai-chat', label: 'محادثة الذكاء الاصطناعي', path: '/entrepreneur/ai-chat', icon: 'MessageSquare', requiresAuth: true },
      { id: 'business-ideas', label: 'أفكار المشاريع', path: '/entrepreneur/business-ideas', icon: 'Lightbulb', requiresAuth: true },
      { id: 'business-plans', label: 'خطط الأعمال', path: '/entrepreneur/business-plans', icon: 'FileText', requiresAuth: true },
      { id: 'funding', label: 'التمويل', path: '/entrepreneur/funding', icon: 'DollarSign', requiresAuth: true },
      { id: 'mentorship', label: 'الإرشاد', path: '/entrepreneur/mentorship', icon: 'Users', requiresAuth: true },
      { id: 'progress', label: 'تتبع التقدم', path: '/entrepreneur/progress', icon: 'TrendingUp', requiresAuth: true },
      { id: 'events', label: 'الفعاليات', path: '/entrepreneur/events', icon: 'Calendar', requiresAuth: true },
      { id: 'resources', label: 'الموارد', path: '/entrepreneur/resources', icon: 'BookOpen', requiresAuth: true },
      { id: 'templates', label: 'القوالب', path: '/entrepreneur/templates', icon: 'Layout', requiresAuth: true },
      { id: 'analytics', label: 'التحليلات', path: '/entrepreneur/analytics', icon: 'BarChart3', requiresAuth: true }
    ];
  }

  /**
   * User Navigation
   */
  private getUserNavigation(): NavigationItem[] {
    return [
      { id: 'dashboard', label: 'لوحة التحكم', path: '/user/dashboard', icon: 'LayoutDashboard', requiresAuth: true },
      { id: 'ai-chat', label: 'محادثة الذكاء الاصطناعي', path: '/user/ai-chat', icon: 'MessageSquare', requiresAuth: true },
      { id: 'learning', label: 'التعلم', path: '/user/learning', icon: 'BookOpen', requiresAuth: true },
      { id: 'community', label: 'المجتمع', path: '/user/community', icon: 'Users', requiresAuth: true }
    ];
  }

  /**
   * Get current user data
   */
  getCurrentUser(): UserWithRoles | null {
    return this.currentUser;
  }

  /**
   * Get all user roles
   */
  getUserRoles(): UserRole[] {
    return this.userRoles;
  }
}

// Export singleton instance
export const roleService = new RoleService();
export default roleService;