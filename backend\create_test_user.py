#!/usr/bin/env python
"""
Create a test user for development and testing
"""
import os
import sys
import django

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'yasmeen_ai.settings')
django.setup()

from django.contrib.auth.models import User
from users.models.main import UserProfile, UserRole, UserRoleAssignment

def create_test_user():
    """Create a test user with admin privileges"""
    try:
        # Check if user already exists
        if User.objects.filter(username='admin').exists():
            user = User.objects.get(username='admin')
            user.set_password('admin123')
            user.save()
            print("[SUCCESS] Updated password for existing admin user")
        else:
            # Create new user
            user = User.objects.create_user(
                username='admin',
                email='<EMAIL>',
                password='admin123',
                is_staff=True,
                is_superuser=True
            )
            print("[SUCCESS] Created new admin user")

        # Create or update user profile
        profile, created = UserProfile.objects.get_or_create(
            user=user,
            defaults={
                'bio': 'Test admin user',
                'location': 'Damascus, Syria',
                'language': 'en'
            }
        )

        if created:
            print("✅ Created user profile")
        else:
            print("✅ User profile already exists")

        # Create admin role if it doesn't exist
        admin_role, role_created = UserRole.objects.get_or_create(
            name='admin',
            defaults={
                'display_name': 'Administrator',
                'description': 'System administrator with full access',
                'permission_level': 'admin',
                'requires_approval': False
            }
        )

        if role_created:
            print("✅ Created admin role")
        else:
            print("✅ Admin role already exists")

        # Assign admin role to user
        role_assignment, assignment_created = UserRoleAssignment.objects.get_or_create(
            user_profile=profile,
            role=admin_role,
            defaults={
                'assigned_by': user,
                'is_active': True,
                'is_approved': True
            }
        )

        if assignment_created:
            print("✅ Assigned admin role to user")
        else:
            print("✅ User already has admin role")

        print(f"Username: admin")
        print(f"Password: admin123")
        print(f"Email: <EMAIL>")
        print(f"Role: {admin_role.display_name}")

        return True

    except Exception as e:
        print(f"❌ Error creating test user: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_super_admin():
    """Create a super admin user"""
    try:
        # Check if super admin already exists
        if User.objects.filter(username='superadmin').exists():
            user = User.objects.get(username='superadmin')
            user.set_password('superadmin123')
            user.save()
            print("✅ Updated password for existing super admin user")
        else:
            # Create new super admin user
            user = User.objects.create_user(
                username='superadmin',
                email='<EMAIL>',
                password='superadmin123',
                is_staff=True,
                is_superuser=True
            )
            print("✅ Created new super admin user")

        # Create or update user profile
        profile, created = UserProfile.objects.get_or_create(
            user=user,
            defaults={
                'bio': 'Super Administrator - Full System Access',
                'location': 'Damascus, Syria',
                'language': 'ar'
            }
        )

        if created:
            print("✅ Created super admin profile")
        else:
            print("✅ Super admin profile already exists")

        # Get super admin role
        try:
            super_admin_role = UserRole.objects.get(name='super_admin')
            print("✅ Found super admin role")
        except UserRole.DoesNotExist:
            print("❌ Super admin role not found. Please run create_roles.py first.")
            return False

        # Assign super admin role to user
        role_assignment, assignment_created = UserRoleAssignment.objects.get_or_create(
            user_profile=profile,
            role=super_admin_role,
            defaults={
                'assigned_by': user,
                'is_active': True,
                'is_approved': True
            }
        )

        if assignment_created:
            print("✅ Assigned super admin role to user")
        else:
            print("✅ User already has super admin role")

        print(f"\n🎉 SUPER ADMIN CREATED SUCCESSFULLY!")
        print(f"Username: superadmin")
        print(f"Password: superadmin123")
        print(f"Email: <EMAIL>")
        print(f"Role: {super_admin_role.display_name}")
        print(f"Access: Full system control and management")

        return True

    except Exception as e:
        print(f"❌ Error creating super admin: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    print("Creating test users...")
    print("\n1. Creating admin user...")
    create_test_user()
    print("\n2. Creating super admin user...")
    create_super_admin()
    print("\n✅ All test users created successfully!")
