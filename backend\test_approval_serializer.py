from django.contrib.auth.models import User
from users.models import UserApproval, RoleApplication
from users.serializers.main import UserApprovalSerializer

# Get the latest user
user = User.objects.order_by('-date_joined').first()
print(f"Testing user: {user.username}")

# Check if UserApproval exists for this user
user_approval = UserApproval.objects.filter(user=user).first()
print(f"UserApproval exists: {user_approval is not None}")

if not user_approval:
    print("Creating UserApproval for testing...")
    user_approval = UserApproval.objects.create(
        user=user,
        status='pending'
    )
    print(f"Created UserApproval: {user_approval}")

# Check RoleApplication
role_app = RoleApplication.objects.filter(user=user).first()
print(f"RoleApplication exists: {role_app is not None}")

if role_app:
    print(f"Role application details:")
    print(f"  Requested role: {role_app.requested_role}")
    print(f"  Investment range: {getattr(role_app, 'investment_range', 'Not found')}")
    print(f"  Portfolio companies: {getattr(role_app, 'portfolio_companies', 'Not found')}")

# Test the UserApprovalSerializer
print("\nTesting UserApprovalSerializer...")
try:
    serializer = UserApprovalSerializer(user_approval)
    data = serializer.data
    
    print("Serializer data keys:", list(data.keys()))
    print(f"User username: {data.get('user_username')}")
    print(f"Status: {data.get('status')}")
    
    # Check role_specific_data
    role_specific = data.get('role_specific_data', {})
    print(f"Role specific data: {role_specific}")
    
    if role_specific:
        print("Role specific data details:")
        for key, value in role_specific.items():
            print(f"  {key}: {value}")
    else:
        print("No role specific data found!")
        
    # Check requested_role_info
    role_info = data.get('requested_role_info', {})
    print(f"Requested role info: {role_info}")
    
except Exception as e:
    print(f"Error with serializer: {e}")
    import traceback
    traceback.print_exc()
