#!/usr/bin/env python3
"""
Check the approval API response to see if motivation and qualifications are included
"""
import requests
import json

def check_approval_api():
    """Check the approval API response"""
    
    url = "http://localhost:8000/api/users/approvals/"
    
    try:
        response = requests.get(url)
        
        print(f"Response Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("API call successful!")
            
            # Look for our test user
            results = data.get('results', [])
            
            for approval in results:
                if approval.get('user_username') == 'testuser_motivation':
                    print(f"\n=== Found Test User: {approval.get('user_username')} ===")
                    
                    # Print all available data
                    print(f"User ID: {approval.get('user_id')}")
                    print(f"Status: {approval.get('status')}")
                    print(f"Role: {approval.get('requested_role_name')}")
                    
                    # Check role-specific data
                    role_data = approval.get('role_specific_data', {})
                    print(f"\nRole-specific data:")
                    print(json.dumps(role_data, indent=2))
                    
                    # Check if motivation and qualifications are in the role info
                    role_info = approval.get('requested_role_info', {})
                    print(f"\nRole info:")
                    print(json.dumps(role_info, indent=2))
                    
                    # Check profile data
                    profile_data = approval.get('profile_summary', {})
                    print(f"\nProfile summary:")
                    print(json.dumps(profile_data, indent=2))
                    
                    # Check for motivation and qualifications specifically
                    motivation = None
                    qualifications = None
                    
                    # Check in role_specific_data
                    if 'motivation' in role_data:
                        motivation = role_data['motivation']
                    if 'qualifications' in role_data:
                        qualifications = role_data['qualifications']
                        
                    # Check in role_info
                    if 'motivation' in role_info:
                        motivation = role_info['motivation']
                    if 'qualifications' in role_info:
                        qualifications = role_info['qualifications']
                    
                    print(f"\n=== Motivation & Qualifications Check ===")
                    print(f"Motivation found: {'YES' if motivation else 'NO'}")
                    if motivation:
                        print(f"Motivation: {motivation}")
                    
                    print(f"Qualifications found: {'YES' if qualifications else 'NO'}")
                    if qualifications:
                        print(f"Qualifications: {qualifications}")
                    
                    return True
                    
            print("Test user not found in approvals")
            print(f"Found {len(results)} total approvals")
            
            # Print all usernames for reference
            usernames = [approval.get('user_username') for approval in results]
            print(f"Available users: {usernames}")
            
        else:
            print(f"API call failed: {response.status_code}")
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"API call failed: {e}")
        
    return False

if __name__ == "__main__":
    check_approval_api()
