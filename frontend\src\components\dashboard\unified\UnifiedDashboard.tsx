/**
 * ✅ UNIFIED DASHBOARD COMPONENT
 * Single dashboard component that adapts based on user role
 * Eliminates all duplicate dashboard implementations
 */

import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../../hooks/useLanguage';
import { useAppSelector, useAppDispatch } from '../../../store/hooks';
import { fetchDashboardStats } from '../../../store/dashboardSlice';
import { DashboardRole, DEFAULT_DASHBOARD_CONFIGS } from '../../../types/dashboard';

// Import section components
import UnifiedWelcomeSection from './sections/UnifiedWelcomeSection';
import UnifiedStatsSection from './sections/UnifiedStatsSection';
import UnifiedQuickActionsSection from './sections/UnifiedQuickActionsSection';
import UnifiedAnalyticsSection from './sections/UnifiedAnalyticsSection';
import UnifiedActivitySection from './sections/UnifiedActivitySection';
import UnifiedNotificationsSection from './sections/UnifiedNotificationsSection';
import UnifiedSystemHealthSection from './sections/UnifiedSystemHealthSection';

interface UnifiedDashboardProps {
  role: DashboardRole;
  className?: string;
}

const UnifiedDashboard: React.FC<UnifiedDashboardProps> = ({ 
  role, 
  className = '' 
}) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const dispatch = useAppDispatch();
  
  // Get dashboard state from Redux
  const { stats, loading, error } = useAppSelector(state => state.dashboard);
  const { user } = useAppSelector(state => state.auth);
  
  // Get role-specific configuration
  const config = DEFAULT_DASHBOARD_CONFIGS[role] || DEFAULT_DASHBOARD_CONFIGS.user;
  
  // Local state
  const [refreshing, setRefreshing] = useState(false);

  // Load dashboard data on mount and role change
  useEffect(() => {
    dispatch(fetchDashboardStats(role));
  }, [dispatch, role]);

  // Auto-refresh based on role configuration
  useEffect(() => {
    if (config.refreshInterval) {
      const interval = setInterval(() => {
        dispatch(fetchDashboardStats(role));
      }, config.refreshInterval);
      
      return () => clearInterval(interval);
    }
  }, [dispatch, role, config.refreshInterval]);

  // Manual refresh handler
  const handleRefresh = async () => {
    setRefreshing(true);
    await dispatch(fetchDashboardStats(role));
    setRefreshing(false);
  };

  // Get sections to display based on role
  const getSectionsForRole = (role: DashboardRole) => {
    const baseSections = ['welcome', 'stats', 'quick_actions'];
    
    switch (role) {
      case 'super_admin':
        return [...baseSections, 'system_health', 'analytics', 'activity', 'notifications'];
      case 'admin':
        return [...baseSections, 'analytics', 'activity', 'notifications'];
      case 'entrepreneur':
        return [...baseSections, 'analytics', 'activity', 'notifications'];
      case 'mentor':
        return [...baseSections, 'activity', 'notifications'];
      case 'investor':
        return [...baseSections, 'analytics', 'activity', 'notifications'];
      case 'moderator':
        return [...baseSections, 'activity', 'notifications'];
      default:
        return [...baseSections, 'activity', 'notifications'];
    }
  };

  const sections = getSectionsForRole(role);

  return (
    <div 
      className={`min-h-screen bg-gradient-to-br ${config.backgroundGradient} p-6 ${isRTL ? 'font-arabic' : ''} ${className}`}
      dir={isRTL ? 'rtl' : 'ltr'}
    >
      <div className="max-w-7xl mx-auto space-y-6">
        
        {/* Header */}
        <div className={`flex items-center justify-between mb-8 ${isRTL ? 'flex-row-reverse' : ''}`}>
          <div className={isRTL ? 'text-right' : 'text-left'}>
            <h1 className="text-3xl font-bold text-white mb-2">
              {isRTL ? t(`dashboard.${role}.title_ar`, config.title) : config.title}
            </h1>
            <p className="text-purple-200">
              {isRTL ? t(`dashboard.${role}.subtitle_ar`, config.subtitle) : config.subtitle}
            </p>
          </div>
          
          {/* Refresh Button */}
          <button
            onClick={handleRefresh}
            disabled={refreshing || loading}
            className={`px-4 py-2 bg-white/10 hover:bg-white/20 text-white rounded-lg transition-colors duration-200 ${
              refreshing || loading ? 'opacity-50 cursor-not-allowed' : ''
            }`}
          >
            {refreshing ? t('common.refreshing', 'Refreshing...') : t('common.refresh', 'Refresh')}
          </button>
        </div>

        {/* Error Message */}
        {error && (
          <div className="bg-red-500/20 border border-red-500/30 rounded-lg p-4 mb-6">
            <p className="text-red-200">{error}</p>
          </div>
        )}

        {/* Dashboard Sections */}
        <div className="space-y-6">
          {sections.includes('welcome') && (
            <UnifiedWelcomeSection role={role} user={user} />
          )}
          
          {sections.includes('stats') && (
            <UnifiedStatsSection role={role} stats={stats} loading={loading} />
          )}
          
          {sections.includes('quick_actions') && (
            <UnifiedQuickActionsSection role={role} />
          )}
          
          {sections.includes('system_health') && role === 'super_admin' && (
            <UnifiedSystemHealthSection />
          )}
          
          {sections.includes('analytics') && (
            <UnifiedAnalyticsSection role={role} stats={stats} />
          )}
          
          {sections.includes('activity') && (
            <UnifiedActivitySection role={role} />
          )}
          
          {sections.includes('notifications') && config.showNotifications && (
            <UnifiedNotificationsSection role={role} />
          )}
        </div>
        
      </div>
    </div>
  );
};

export default UnifiedDashboard;
