import React from 'react';
import { useTranslation } from 'react-i18next';
import { 
  Server, 
  Database, 
  Shield, 
  Settings, 
  Activity, 
  AlertTriangle, 
  CheckCircle, 
  Clock,
  HardDrive,
  Cpu,
  Wifi,
  RefreshCw
} from 'lucide-react';

const SystemManagementPage: React.FC = () => {
  const { t } = useTranslation();

  // Mock system data - replace with real API call
  const systemStatus = {
    server: {
      status: 'healthy',
      uptime: '15 يوم، 8 ساعات',
      cpu: 45,
      memory: 62,
      disk: 78
    },
    database: {
      status: 'healthy',
      connections: 12,
      queries: 1547,
      size: '2.3 GB'
    },
    services: [
      { name: 'خدمة المصادقة', status: 'running', lastCheck: '2025-07-25T17:30:00Z' },
      { name: 'خدمة الذكاء الاصطناعي', status: 'running', lastCheck: '2025-07-25T17:29:00Z' },
      { name: 'خدمة الإشعارات', status: 'warning', lastCheck: '2025-07-25T17:25:00Z' },
      { name: 'خدمة التخزين', status: 'running', lastCheck: '2025-07-25T17:30:00Z' },
      { name: 'خدمة البريد الإلكتروني', status: 'error', lastCheck: '2025-07-25T17:20:00Z' }
    ],
    logs: [
      { level: 'info', message: 'تم تسجيل دخول مستخدم جديد', timestamp: '2025-07-25T17:30:15Z' },
      { level: 'warning', message: 'استخدام الذاكرة مرتفع (85%)', timestamp: '2025-07-25T17:28:42Z' },
      { level: 'error', message: 'فشل في إرسال البريد الإلكتروني', timestamp: '2025-07-25T17:25:33Z' },
      { level: 'info', message: 'تم إنشاء نسخة احتياطية من قاعدة البيانات', timestamp: '2025-07-25T17:20:00Z' }
    ]
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy':
      case 'running': return 'text-green-400';
      case 'warning': return 'text-yellow-400';
      case 'error': return 'text-red-400';
      default: return 'text-gray-400';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy':
      case 'running': return <CheckCircle className="w-5 h-5" />;
      case 'warning': return <AlertTriangle className="w-5 h-5" />;
      case 'error': return <AlertTriangle className="w-5 h-5" />;
      default: return <Clock className="w-5 h-5" />;
    }
  };

  const getLogLevelColor = (level: string) => {
    switch (level) {
      case 'info': return 'text-blue-400';
      case 'warning': return 'text-yellow-400';
      case 'error': return 'text-red-400';
      default: return 'text-gray-400';
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-indigo-950 to-purple-950 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-white mb-2">
              ⚙️ إدارة النظام
            </h1>
            <p className="text-gray-300">
              مراقبة حالة النظام والخوادم والخدمات
            </p>
          </div>
          <button className="mt-4 sm:mt-0 flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
            <RefreshCw className="w-5 h-5" />
            تحديث الحالة
          </button>
        </div>

        {/* System Overview */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
          {/* Server Status */}
          <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
            <div className="flex items-center gap-3 mb-6">
              <div className="p-3 bg-blue-500/20 rounded-lg">
                <Server className="w-6 h-6 text-blue-400" />
              </div>
              <div>
                <h3 className="text-xl font-bold text-white">حالة الخادم</h3>
                <p className="text-gray-400">مراقبة أداء الخادم</p>
              </div>
            </div>

            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-gray-300">الحالة</span>
                <div className={`flex items-center gap-2 ${getStatusColor(systemStatus.server.status)}`}>
                  {getStatusIcon(systemStatus.server.status)}
                  <span>صحي</span>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-gray-300">وقت التشغيل</span>
                <span className="text-white">{systemStatus.server.uptime}</span>
              </div>

              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-gray-300 flex items-center gap-2">
                    <Cpu className="w-4 h-4" />
                    المعالج
                  </span>
                  <span className="text-white">{systemStatus.server.cpu}%</span>
                </div>
                <div className="w-full bg-gray-700 rounded-full h-2">
                  <div 
                    className="bg-blue-500 h-2 rounded-full" 
                    style={{ width: `${systemStatus.server.cpu}%` }}
                  ></div>
                </div>
              </div>

              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-gray-300 flex items-center gap-2">
                    <Activity className="w-4 h-4" />
                    الذاكرة
                  </span>
                  <span className="text-white">{systemStatus.server.memory}%</span>
                </div>
                <div className="w-full bg-gray-700 rounded-full h-2">
                  <div 
                    className="bg-green-500 h-2 rounded-full" 
                    style={{ width: `${systemStatus.server.memory}%` }}
                  ></div>
                </div>
              </div>

              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-gray-300 flex items-center gap-2">
                    <HardDrive className="w-4 h-4" />
                    التخزين
                  </span>
                  <span className="text-white">{systemStatus.server.disk}%</span>
                </div>
                <div className="w-full bg-gray-700 rounded-full h-2">
                  <div 
                    className="bg-yellow-500 h-2 rounded-full" 
                    style={{ width: `${systemStatus.server.disk}%` }}
                  ></div>
                </div>
              </div>
            </div>
          </div>

          {/* Database Status */}
          <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
            <div className="flex items-center gap-3 mb-6">
              <div className="p-3 bg-green-500/20 rounded-lg">
                <Database className="w-6 h-6 text-green-400" />
              </div>
              <div>
                <h3 className="text-xl font-bold text-white">قاعدة البيانات</h3>
                <p className="text-gray-400">حالة قاعدة البيانات</p>
              </div>
            </div>

            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-gray-300">الحالة</span>
                <div className={`flex items-center gap-2 ${getStatusColor(systemStatus.database.status)}`}>
                  {getStatusIcon(systemStatus.database.status)}
                  <span>صحي</span>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-gray-300">الاتصالات النشطة</span>
                <span className="text-white">{systemStatus.database.connections}</span>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-gray-300">الاستعلامات اليوم</span>
                <span className="text-white">{systemStatus.database.queries.toLocaleString()}</span>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-gray-300">حجم قاعدة البيانات</span>
                <span className="text-white">{systemStatus.database.size}</span>
              </div>
            </div>
          </div>
        </div>

        {/* Services Status */}
        <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20 mb-8">
          <h3 className="text-xl font-bold text-white mb-6 flex items-center gap-2">
            <Wifi className="w-5 h-5" />
            حالة الخدمات
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {systemStatus.services.map((service, index) => (
              <div key={index} className="bg-white/5 rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-white font-medium">{service.name}</span>
                  <div className={`flex items-center gap-1 ${getStatusColor(service.status)}`}>
                    {getStatusIcon(service.status)}
                  </div>
                </div>
                <p className="text-gray-400 text-sm">
                  آخر فحص: {new Date(service.lastCheck).toLocaleTimeString('ar-SA')}
                </p>
              </div>
            ))}
          </div>
        </div>

        {/* System Logs */}
        <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
          <h3 className="text-xl font-bold text-white mb-6 flex items-center gap-2">
            <Settings className="w-5 h-5" />
            سجلات النظام
          </h3>
          <div className="space-y-3">
            {systemStatus.logs.map((log, index) => (
              <div key={index} className="flex items-start gap-3 p-3 bg-white/5 rounded-lg">
                <div className={`mt-1 ${getLogLevelColor(log.level)}`}>
                  <div className="w-2 h-2 rounded-full bg-current"></div>
                </div>
                <div className="flex-1">
                  <p className="text-white">{log.message}</p>
                  <p className="text-gray-400 text-sm">
                    {new Date(log.timestamp).toLocaleString('ar-SA')}
                  </p>
                </div>
                <span className={`text-xs px-2 py-1 rounded ${
                  log.level === 'error' ? 'bg-red-500/20 text-red-400' :
                  log.level === 'warning' ? 'bg-yellow-500/20 text-yellow-400' :
                  'bg-blue-500/20 text-blue-400'
                }`}>
                  {log.level === 'error' ? 'خطأ' : log.level === 'warning' ? 'تحذير' : 'معلومات'}
                </span>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default SystemManagementPage;