import React from 'react';
import { useTranslation } from 'react-i18next';
import { useQuery } from '@tanstack/react-query';
import { FileText, Plus, Search, Filter, Eye, Edit, Trash2, CheckCircle, XCircle, AlertTriangle } from 'lucide-react';
import { adminAPI } from '../../services/api';

const ContentManagementPage: React.FC = () => {
  const { t } = useTranslation();

  // 🔥 REAL DATA: Fetch actual content from API
  const { data: apiContent = [], isLoading, error } = useQuery({
    queryKey: ['admin', 'content'],
    queryFn: () => adminAPI.getContent(),
    refetchInterval: 30000, // Refresh every 30 seconds
    retry: 3,
  });

  // Use real data if available, otherwise fallback to sample data for demo
  const content = React.useMemo(() => {
    if (apiContent && apiContent.length > 0) {
      return apiContent;
    }

    // Fallback sample data when no real content exists
    return [
      {
        id: 1,
        title: 'دليل ريادة الأعمال',
        type: 'article',
        author: 'فريق المحتوى',
        status: 'published',
        views: 1250,
        createdAt: '2025-07-20',
        updatedAt: '2025-07-22'
      },
      {
        id: 2,
        title: 'كيفية الحصول على التمويل',
        type: 'video',
        author: 'خبير التمويل',
        status: 'review',
        views: 890,
        createdAt: '2025-07-18',
        updatedAt: '2025-07-24'
      },
      {
        id: 3,
        title: 'ورشة عمل الابتكار',
        type: 'event',
        author: 'منظم الفعاليات',
        status: 'flagged',
        views: 456,
        createdAt: '2025-07-15',
        updatedAt: '2025-07-25'
      },
      {
        id: 4,
        title: 'نصائح للمستثمرين',
        type: 'guide',
        author: 'مستشار الاستثمار',
        status: 'published',
        views: 2100,
        createdAt: '2025-07-10',
        updatedAt: '2025-07-20'
      }
    ];
  }, [apiContent]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'published': return 'bg-green-100 text-green-800';
      case 'review': return 'bg-yellow-100 text-yellow-800';
      case 'flagged': return 'bg-red-100 text-red-800';
      case 'draft': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'published': return 'منشور';
      case 'review': return 'مراجعة';
      case 'flagged': return 'مبلغ عنه';
      case 'draft': return 'مسودة';
      default: return status;
    }
  };

  const getTypeText = (type: string) => {
    switch (type) {
      case 'article': return 'مقال';
      case 'video': return 'فيديو';
      case 'event': return 'فعالية';
      case 'guide': return 'دليل';
      default: return type;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-indigo-950 to-purple-950 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-white mb-2">
              📝 إدارة المحتوى
            </h1>
            <p className="text-gray-300">
              إدارة المقالات والفيديوهات والفعاليات والمحتوى التعليمي
            </p>
          </div>
          <button className="mt-4 sm:mt-0 flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
            <Plus className="w-5 h-5" />
            إضافة محتوى جديد
          </button>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
            <div className="flex items-center gap-4">
              <div className="p-3 bg-blue-500/20 rounded-lg">
                <FileText className="w-6 h-6 text-blue-400" />
              </div>
              <div>
                <p className="text-2xl font-bold text-white">{content.length}</p>
                <p className="text-gray-400">إجمالي المحتوى</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
            <div className="flex items-center gap-4">
              <div className="p-3 bg-green-500/20 rounded-lg">
                <CheckCircle className="w-6 h-6 text-green-400" />
              </div>
              <div>
                <p className="text-2xl font-bold text-white">
                  {content.filter(c => c.status === 'published').length}
                </p>
                <p className="text-gray-400">محتوى منشور</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
            <div className="flex items-center gap-4">
              <div className="p-3 bg-yellow-500/20 rounded-lg">
                <AlertTriangle className="w-6 h-6 text-yellow-400" />
              </div>
              <div>
                <p className="text-2xl font-bold text-white">
                  {content.filter(c => c.status === 'review').length}
                </p>
                <p className="text-gray-400">في المراجعة</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
            <div className="flex items-center gap-4">
              <div className="p-3 bg-red-500/20 rounded-lg">
                <XCircle className="w-6 h-6 text-red-400" />
              </div>
              <div>
                <p className="text-2xl font-bold text-white">
                  {content.filter(c => c.status === 'flagged').length}
                </p>
                <p className="text-gray-400">محتوى مبلغ عنه</p>
              </div>
            </div>
          </div>
        </div>

        {/* Search and Filters */}
        <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20 mb-8">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="text"
                placeholder="البحث في المحتوى..."
                className="w-full pl-10 pr-4 py-2 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <select className="px-4 py-2 bg-white/10 border border-white/20 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500">
              <option value="">جميع الأنواع</option>
              <option value="article">مقالات</option>
              <option value="video">فيديوهات</option>
              <option value="event">فعاليات</option>
              <option value="guide">أدلة</option>
            </select>
            <select className="px-4 py-2 bg-white/10 border border-white/20 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500">
              <option value="">جميع الحالات</option>
              <option value="published">منشور</option>
              <option value="review">مراجعة</option>
              <option value="flagged">مبلغ عنه</option>
              <option value="draft">مسودة</option>
            </select>
          </div>
        </div>

        {/* Content Table */}
        <div className="bg-white/10 backdrop-blur-sm rounded-xl border border-white/20 overflow-hidden">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-white/5">
                <tr>
                  <th className="px-6 py-4 text-right text-sm font-medium text-gray-300">العنوان</th>
                  <th className="px-6 py-4 text-right text-sm font-medium text-gray-300">النوع</th>
                  <th className="px-6 py-4 text-right text-sm font-medium text-gray-300">المؤلف</th>
                  <th className="px-6 py-4 text-right text-sm font-medium text-gray-300">الحالة</th>
                  <th className="px-6 py-4 text-right text-sm font-medium text-gray-300">المشاهدات</th>
                  <th className="px-6 py-4 text-right text-sm font-medium text-gray-300">تاريخ الإنشاء</th>
                  <th className="px-6 py-4 text-right text-sm font-medium text-gray-300">الإجراءات</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-white/10">
                {isLoading ? (
                  <tr>
                    <td colSpan={7} className="px-6 py-8 text-center text-gray-400">
                      جاري تحميل المحتوى...
                    </td>
                  </tr>
                ) : error ? (
                  <tr>
                    <td colSpan={7} className="px-6 py-8 text-center text-red-400">
                      خطأ في تحميل المحتوى: {error.message}
                    </td>
                  </tr>
                ) : content.length === 0 ? (
                  <tr>
                    <td colSpan={7} className="px-6 py-8 text-center text-gray-400">
                      لا يوجد محتوى
                    </td>
                  </tr>
                ) : (
                  content.map((item) => (
                    <tr key={item.id} className="hover:bg-white/5 transition-colors">
                      <td className="px-6 py-4">
                        <div>
                          <p className="text-white font-medium">{item.title}</p>
                          <p className="text-gray-400 text-sm">
                            آخر تحديث: {new Date(item.updated_at || item.updatedAt).toLocaleDateString('ar-SA')}
                          </p>
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                          {getTypeText(item.type)}
                        </span>
                      </td>
                      <td className="px-6 py-4 text-gray-300">
                        {item.author}
                      </td>
                      <td className="px-6 py-4">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(item.status)}`}>
                          {getStatusText(item.status)}
                        </span>
                      </td>
                      <td className="px-6 py-4 text-gray-300">
                        {item.views?.toLocaleString() || '0'}
                      </td>
                      <td className="px-6 py-4 text-gray-300">
                        {new Date(item.created_at || item.createdAt).toLocaleDateString('ar-SA')}
                      </td>
                      <td className="px-6 py-4">
                        <div className="flex items-center gap-2">
                          <button className="p-2 text-gray-400 hover:text-blue-400 hover:bg-blue-500/20 rounded-lg transition-colors">
                            <Eye className="w-4 h-4" />
                          </button>
                          <button className="p-2 text-gray-400 hover:text-green-400 hover:bg-green-500/20 rounded-lg transition-colors">
                            <Edit className="w-4 h-4" />
                          </button>
                          <button className="p-2 text-gray-400 hover:text-red-400 hover:bg-red-500/20 rounded-lg transition-colors">
                            <Trash2 className="w-4 h-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ContentManagementPage;