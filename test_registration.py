#!/usr/bin/env python3
"""
Test script to verify that all registration fields are being saved and displayed correctly
"""

import requests
import json

# Test registration data with all fields
test_data = {
    # Basic user info
    "first_name": "Test",
    "last_name": "Entrepreneur", 
    "username": "testentrepreneur123",
    "email": "<EMAIL>",
    "password": "TestPass123!",
    "password_confirm": "TestPass123!",
    "selected_role": "entrepreneur",
    
    # Profile info
    "phone": "+1234567890",
    "location": "Test City",
    "company": "Test Company",
    "job_title": "Founder",
    "bio": "Test bio for entrepreneur",
    
    # Entrepreneur specific fields
    "business_name": "Test Startup",
    "business_stage": "mvp",
    "industry": "Technology",
    "funding_needed": "100k-500k",
    "business_description": "A revolutionary test startup that will change the world",
    "team_size": "2-3",
    "support_needed": "Mentorship, funding, technical guidance",
    "previous_experience": "Previously founded a small tech company",
    
    # General fields
    "portfolio_url": "https://testentrepreneur.com/portfolio",
    "experience": "5 years in tech industry"
}

def test_registration():
    """Test the registration endpoint"""
    url = "http://localhost:8000/api/auth/register/"
    
    try:
        response = requests.post(url, json=test_data)
        print(f"Registration Response Status: {response.status_code}")
        print(f"Registration Response: {response.json()}")
        
        if response.status_code == 201:
            print("Registration successful!")
            return True
        else:
            print("Registration failed!")
            return False

    except Exception as e:
        print(f"Registration error: {e}")
        return False

def test_approval_data():
    """Test the user approval endpoint to see what data is returned"""
    # First, we need to login as super admin
    login_url = "http://localhost:8000/api/auth/login/"
    login_data = {
        "username": "admin",  # Assuming super admin username
        "password": "admin123"  # Assuming super admin password
    }
    
    try:
        # Login
        login_response = requests.post(login_url, json=login_data)
        if login_response.status_code != 200:
            print(f"Login failed: {login_response.status_code}")
            return False
            
        token = login_response.json().get('access')
        headers = {'Authorization': f'Bearer {token}'}
        
        # Get user approvals
        approvals_url = "http://localhost:8000/api/admin/user-approvals/"
        approvals_response = requests.get(approvals_url, headers=headers)
        
        print(f"Approvals Response Status: {approvals_response.status_code}")
        
        if approvals_response.status_code == 200:
            approvals = approvals_response.json()
            print(f"Found {len(approvals)} pending approvals")

            # Check the latest approval for our test user
            for approval in approvals:
                if approval.get('user', {}).get('username') == 'testentrepreneur123':
                    print("Found test user approval!")
                    print("Role-specific data:")
                    role_data = approval.get('role_specific_data', {})
                    for key, value in role_data.items():
                        print(f"  {key}: {value}")
                    return True

            print("Test user approval not found")
            return False
        else:
            print(f"Failed to get approvals: {approvals_response.status_code}")
            return False

    except Exception as e:
        print(f"Approval test error: {e}")
        return False

if __name__ == "__main__":
    print("Testing Registration and Approval Flow...")
    print("=" * 50)

    # Test registration
    if test_registration():
        print("\nWaiting for registration to process...")
        import time
        time.sleep(2)

        # Test approval data
        test_approval_data()

    print("\n" + "=" * 50)
    print("Test completed!")
