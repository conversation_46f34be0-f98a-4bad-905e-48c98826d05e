#!/usr/bin/env python3
"""
Test script to verify data consistency in the database after registration.
This script directly checks the database to ensure all fields are properly stored.
"""

import os
import sys
import django

# Add the current directory to the Python path (assuming we're running from backend)
sys.path.append(os.path.dirname(__file__))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

from django.contrib.auth.models import User
from users.models import UserProfile, RoleApplication

def test_latest_investor_registration():
    """Test the latest investor registration data"""
    print("Testing Latest Investor Registration Data...")
    print("=" * 50)
    
    try:
        # Get the latest user with investor role
        latest_user = User.objects.filter(
            role_applications__role_name='investor'
        ).order_by('-date_joined').first()
        
        if not latest_user:
            print("No investor users found in database")
            return False
            
        print(f"Found user: {latest_user.username} (ID: {latest_user.id})")
        print(f"Email: {latest_user.email}")
        print(f"Date joined: {latest_user.date_joined}")
        
        # Check UserProfile
        try:
            profile = latest_user.profile
            print("\nUserProfile data:")
            print(f"  Location: {profile.location}")
            print(f"  Company: {profile.company}")
            print(f"  Job Title: {profile.job_title}")
            print(f"  Phone: {profile.phone_number}")
            print(f"  Bio: {profile.bio}")
            print(f"  Language: {profile.language}")
            
            # Check investor-specific fields in UserProfile
            print("\nInvestor fields in UserProfile:")
            print(f"  Investment Range: {profile.investment_range}")
            print(f"  Investment Stage: {profile.investment_stage}")
            print(f"  Preferred Industries: {profile.preferred_industries}")
            print(f"  Investment Criteria: {profile.investment_criteria}")
            print(f"  Portfolio Companies: {profile.portfolio_companies}")
            print(f"  Team Size: {profile.team_size}")
            print(f"  Support Needed: {profile.support_needed}")
            print(f"  Previous Experience: {profile.previous_experience}")
            print(f"  Preferred Communication: {profile.preferred_communication}")
            
        except UserProfile.DoesNotExist:
            print("UserProfile not found for this user")
            return False
        
        # Check RoleApplication
        try:
            role_app = RoleApplication.objects.filter(
                user=latest_user,
                role_name='investor'
            ).first()
            
            if role_app:
                print("\nRoleApplication data:")
                print(f"  Role Name: {role_app.role_name}")
                print(f"  Status: {role_app.status}")
                print(f"  Investment Range: {role_app.investment_range}")
                print(f"  Investment Focus: {role_app.investment_focus}")
                print(f"  Investment Stage: {role_app.investment_stage}")
                print(f"  Portfolio Companies: {role_app.portfolio_companies}")
                print(f"  Due Diligence Requirements: {role_app.due_diligence_requirements}")
                print(f"  Created: {role_app.created_at}")
            else:
                print("RoleApplication not found for this user")
                return False
                
        except Exception as e:
            print(f"Error accessing RoleApplication: {e}")
            return False
        
        # Check for data consistency
        print("\nData Consistency Check:")
        issues = []
        
        # Check if portfolio_companies exists in both places
        if hasattr(profile, 'portfolio_companies') and hasattr(role_app, 'portfolio_companies'):
            if profile.portfolio_companies != role_app.portfolio_companies:
                issues.append(f"Portfolio companies mismatch: Profile='{profile.portfolio_companies}', RoleApp='{role_app.portfolio_companies}'")
        
        # Check if investment_range exists in both places
        if hasattr(profile, 'investment_range') and hasattr(role_app, 'investment_range'):
            if profile.investment_range != role_app.investment_range:
                issues.append(f"Investment range mismatch: Profile='{profile.investment_range}', RoleApp='{role_app.investment_range}'")
        
        if issues:
            print("Issues found:")
            for issue in issues:
                print(f"  - {issue}")
            return False
        else:
            print("All data is consistent!")
            return True
            
    except Exception as e:
        print(f"Test failed with exception: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_model_fields():
    """Check what fields are available in the models"""
    print("\nModel Field Analysis:")
    print("=" * 30)
    
    print("UserProfile fields:")
    for field in UserProfile._meta.fields:
        print(f"  - {field.name}: {field.__class__.__name__}")
    
    print("\nRoleApplication fields:")
    for field in RoleApplication._meta.fields:
        print(f"  - {field.name}: {field.__class__.__name__}")

if __name__ == "__main__":
    print("Starting Database Data Consistency Test")
    print("=" * 50)
    
    check_model_fields()
    
    print("\n")
    success = test_latest_investor_registration()
    
    print("=" * 50)
    if success:
        print("All tests passed! Data is consistent in the database.")
    else:
        print("Tests failed! There are data consistency issues.")
        sys.exit(1)
    
    print("Test completed!")
