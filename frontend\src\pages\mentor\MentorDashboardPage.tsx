import React from 'react';
import { UnifiedDashboard } from '../../components/dashboard/unified';

/**
 * ✅ CONSOLIDATED MENTOR DASHBOARD PAGE
 *
 * Now uses UnifiedDashboard with mentor role configuration.
 * Provides mentorship tools through unified architecture.
 *
 * Key Features:
 * - Unified dashboard with mentor privileges
 * - Mentee management and session scheduling
 * - Expertise sharing and content creation
 * - Progress tracking and feedback tools
 * - Mentorship analytics and insights
 * - Consistent UI with role-specific sections
 * - Maintainable single-source architecture
 */
const MentorDashboardPage: React.FC = () => {
  return <UnifiedDashboard role="mentor" />;
};

export default MentorDashboardPage;
