import requests
import json

# Test login with different credentials
base_url = "http://localhost:8000"

credentials_to_test = [
    {"username": "admin", "password": "admin123"},
    {"username": "admin", "password": "admin"},
    {"username": "superadmin", "password": "admin123"},
    {"username": "superadmin", "password": "superadmin123"},
]

print("Testing login credentials...")

for creds in credentials_to_test:
    try:
        print(f"\nTesting: {creds['username']} / {creds['password']}")
        
        response = requests.post(f"{base_url}/api/auth/login/", json=creds)
        
        if response.status_code == 200:
            data = response.json()
            print(f"SUCCESS! Login works")
            print(f"   Access token: {data.get('access', 'N/A')[:50]}...")
            print(f"   User info: {data.get('user', {})}")

            # Test accessing approvals endpoint
            token = data.get('access')
            if token:
                headers = {'Authorization': f'Bearer {token}'}
                approvals_response = requests.get(f"{base_url}/api/users/approvals/", headers=headers)
                print(f"   Approvals access: {approvals_response.status_code == 200}")
                if approvals_response.status_code == 200:
                    approvals_data = approvals_response.json()
                    count = len(approvals_data.get('results', []))
                    print(f"   Found {count} approval records")
            break
        else:
            print(f"Failed: {response.status_code}")
            print(f"   Response: {response.text}")

    except Exception as e:
        print(f"Error: {e}")

else:
    print("\nNone of the credentials worked!")
    print("You may need to create a superuser or reset password")
    print("Run: python manage.py createsuperuser")
