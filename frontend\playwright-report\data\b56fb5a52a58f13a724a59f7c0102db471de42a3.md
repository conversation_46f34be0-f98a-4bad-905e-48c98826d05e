# Page snapshot

```yaml
- heading "Yasmeen AI" [level=1]
- paragraph: User
- button "Toggle sidebar":
  - img
- img
- paragraph
- paragraph: "@testuser"
- img
- text: User
- navigation:
  - button "Dashboard":
    - img
    - text: Dashboard
  - button "Profile":
    - img
    - text: Profile
  - button "Settings":
    - img
    - text: Settings
  - button "AI Chat":
    - img
    - text: AI Chat
- button "Logout":
  - img
  - text: Logout
- banner:
  - heading "Dashboard" [level=1]
  - img
  - text: testuser
- main:
  - heading "Welcome, Entrepreneur" [level=1]
  - paragraph: Your dashboard - track progress and manage your entrepreneurial journey
  - button "12":
    - img
    - text: "12"
  - button:
    - img
  - img
  - heading "1,247" [level=3]
  - paragraph: Total Startups
  - img
  - heading "89" [level=3]
  - paragraph: Active Mentorships
  - img
  - heading "$12.5M" [level=3]
  - paragraph: Funding Raised
  - img
  - heading "73%" [level=3]
  - paragraph: Success Rate
  - heading "Quick Actions" [level=2]
  - img
  - heading "Create Business Plan" [level=5]
  - paragraph: Start building your business plan with AI assistance
  - button "Get Started"
  - img
  - heading "Find a Mentor" [level=5]
  - paragraph: Connect with experienced mentors in your industry
  - button "Get Started"
  - img
  - heading "Explore Funding" [level=5]
  - paragraph: Discover funding opportunities for your startup
  - button "Get Started"
  - img
  - heading "AI Business Analysis" [level=5]
  - paragraph: Get AI-powered insights for your business
  - button "Get Started"
  - img
  - heading "Upcoming Events" [level=5]
  - paragraph: Join workshops, webinars, and networking events
  - button "Get Started"
  - img
  - heading "Community Forum" [level=5]
  - paragraph: Connect with other entrepreneurs and share ideas
  - button "Get Started"
  - heading "Recent Activity" [level=3]
  - paragraph: Business plan updated - 1 hours ago
  - paragraph: Business plan updated - 2 hours ago
  - paragraph: Business plan updated - 3 hours ago
  - heading "Upcoming Goals" [level=3]
  - img
  - text: Goal 1 1 days
  - img
  - text: Goal 2 2 days
  - img
  - text: Goal 3 3 days
- button "📊 Perf"
- button "📊 Perf N/A"
- button "Open Tanstack query devtools":
  - img
```