import requests
import json

# Test the API endpoint directly
base_url = "http://localhost:8000"

# First, login as admin to get token
login_data = {
    "username": "admin",
    "password": "admin123"
}

try:
    print("Step 1: Logging in as super admin...")
    login_response = requests.post(f"{base_url}/api/auth/login/", json=login_data)
    
    if login_response.status_code != 200:
        print(f"Login failed: {login_response.status_code}")
        print(f"Response: {login_response.text}")
        exit(1)
        
    token = login_response.json().get('access')
    headers = {'Authorization': f'Bearer {token}'}
    print("Login successful!")
    
    # Step 2: Get user approvals
    print("\nStep 2: Getting user approvals...")
    approvals_url = f"{base_url}/api/users/approvals/"
    approvals_response = requests.get(approvals_url, headers=headers)
    
    if approvals_response.status_code != 200:
        print(f"Failed to get approvals: {approvals_response.status_code}")
        print(f"Response: {approvals_response.text}")
        exit(1)
    
    approvals_data = approvals_response.json()
    print(f"Response type: {type(approvals_data)}")
    print(f"Response keys: {list(approvals_data.keys()) if isinstance(approvals_data, dict) else 'Not a dict'}")

    # Handle different response formats
    if isinstance(approvals_data, dict):
        if 'results' in approvals_data:
            approvals_list = approvals_data['results']
        else:
            print("Unexpected dict format:", approvals_data)
            exit(1)
    else:
        approvals_list = approvals_data

    print(f"Got {len(approvals_list)} approvals")

    # Check all approvals to find our test investor
    if approvals_list:
        print(f"\nAll approvals:")
        for i, approval in enumerate(approvals_list):
            username = approval.get('user_username')
            status = approval.get('status')
            role_specific = approval.get('role_specific_data', {})
            print(f"  {i}: {username} - {status} - Has role data: {bool(role_specific)}")

        # Find the test investor
        test_investor = None
        for approval in approvals_list:
            if approval.get('user_username') == 'test_investor_123':
                test_investor = approval
                break

        if test_investor:
            print(f"\nTest investor approval data:")
            print(f"User: {test_investor.get('user_username')}")
            print(f"Status: {test_investor.get('status')}")

            # Check role_specific_data
            role_specific = test_investor.get('role_specific_data', {})
            print(f"\nRole specific data:")
            if role_specific:
                for key, value in role_specific.items():
                    print(f"  {key}: {value}")
            else:
                print("  No role specific data found!")

            # Check requested_role_info
            role_info = test_investor.get('requested_role_info', {})
            print(f"\nRequested role info:")
            if role_info:
                for key, value in role_info.items():
                    print(f"  {key}: {value}")
            else:
                print("  No requested role info found!")
        else:
            print("\nTest investor not found in approvals!")
    else:
        print("No approvals found!")
        
except Exception as e:
    print(f"Error: {e}")
    import traceback
    traceback.print_exc()
