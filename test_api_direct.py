#!/usr/bin/env python3
"""
Test the API directly to see if our user appears
"""

import os
import sys
import django
import requests
import json

# Add the backend directory to Python path
backend_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'backend')
sys.path.append(backend_path)

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'yasmeen_ai.settings')
django.setup()

def test_api_direct():
    """Test the API directly"""
    
    # Login as superadmin
    login_data = {
        "username": "superadmin",
        "password": "superadmin123"
    }
    
    try:
        login_response = requests.post(
            'http://localhost:8000/api/auth/login/',
            json=login_data,
            headers={'Content-Type': 'application/json'}
        )
        
        if login_response.status_code != 200:
            print(f"Login failed: {login_response.status_code}")
            return
        
        access_token = login_response.json().get('access')
        
        # Get approvals data
        headers = {
            'Authorization': f'Bearer {access_token}',
            'Content-Type': 'application/json'
        }
        
        approvals_response = requests.get(
            'http://localhost:8000/api/users/approvals/',
            headers=headers
        )
        
        if approvals_response.status_code != 200:
            print(f"Approvals API failed: {approvals_response.status_code}")
            print(f"Response: {approvals_response.text}")
            return
        
        approvals_data = approvals_response.json()
        
        print(f"API Response structure:")
        print(f"  Type: {type(approvals_data)}")
        print(f"  Keys: {list(approvals_data.keys()) if isinstance(approvals_data, dict) else 'Not a dict'}")
        
        # Get the results
        if isinstance(approvals_data, dict) and 'results' in approvals_data:
            results = approvals_data['results']
            print(f"  Results count: {len(results)}")
            print(f"  Total count: {approvals_data.get('count', 'N/A')}")
            print(f"  Next page: {approvals_data.get('next', 'None')}")
            print(f"  Previous page: {approvals_data.get('previous', 'None')}")
        else:
            results = approvals_data
            print(f"  Direct results count: {len(results) if isinstance(results, list) else 'Not a list'}")
        
        # Look for our test user
        target_username = 'debug_test_20250726_153950'
        found_user = None
        
        print(f"\nLooking for user: {target_username}")
        
        for user in results:
            username = user.get('user_username', '')
            print(f"  Found user: {username}")
            if username == target_username:
                found_user = user
                break
        
        if found_user:
            print(f"\nOK Found our test user!")
            print(f"  ID: {found_user.get('id')}")
            print(f"  Status: {found_user.get('status')}")
            
            # Check motivation and qualifications
            role_info = found_user.get('requested_role_info', {})
            if role_info:
                motivation = role_info.get('motivation', 'NOT FOUND')
                qualifications = role_info.get('qualifications', 'NOT FOUND')
                print(f"  Motivation: {motivation}")
                print(f"  Qualifications: {qualifications}")
                
                # Check if real data
                if "This is my test motivation" in motivation:
                    print("  OK Motivation is real test data")
                else:
                    print("  ERROR Motivation is not our test data")
                    
                if "These are my test qualifications" in qualifications:
                    print("  OK Qualifications is real test data")
                else:
                    print("  ERROR Qualifications is not our test data")
            else:
                print("  ERROR No role info found")
        else:
            print(f"\nERROR User {target_username} not found in API response")
            print(f"Available usernames:")
            for user in results[:10]:  # Show first 10
                print(f"  - {user.get('user_username', 'NO USERNAME')}")

            # Check if there's a next page
            if isinstance(approvals_data, dict) and approvals_data.get('next'):
                print(f"\nChecking next page...")
                next_response = requests.get(approvals_data['next'], headers=headers)
                if next_response.status_code == 200:
                    next_data = next_response.json()
                    next_results = next_data.get('results', [])
                    print(f"Next page has {len(next_results)} results:")
                    for user in next_results:
                        username = user.get('user_username', '')
                        print(f"  - {username}")
                        if username == target_username:
                            print(f"  FOUND our test user on page 2!")
                            role_info = user.get('requested_role_info', {})
                            if role_info:
                                motivation = role_info.get('motivation', 'NOT FOUND')
                                qualifications = role_info.get('qualifications', 'NOT FOUND')
                                print(f"    Motivation: {motivation}")
                                print(f"    Qualifications: {qualifications}")
                else:
                    print(f"Failed to get next page: {next_response.status_code}")
        
    except Exception as e:
        print(f"Error: {str(e)}")

if __name__ == "__main__":
    test_api_direct()
