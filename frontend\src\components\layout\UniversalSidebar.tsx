/**
 * ✅ CLEAN REDUX-BASED UNIVERSAL SIDEBAR
 * Simple, unified sidebar using only Redux state management
 * Replaces all old duplicate sidebar components
 */

import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAppSelector, useAppDispatch } from '../../store/hooks';
import { logout } from '../../store/authSlice';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
import useReduxRoles from '../../hooks/useReduxRoles';
import LanguageSwitcher from '../common/LanguageSwitcher';
import { getNavigationName } from '../../utils/navigationConfig';
import { renderIcon } from '../../utils/iconMapping';
import {
  Home, User, Settings, Bot, LogOut, X, Menu,
  Shield, BarChart3, Users, FileText, Calendar,
  TrendingUp, Briefcase, DollarSign, BookOpen,
  Plus, Search, Flag, Folder
} from 'lucide-react';

interface UniversalSidebarProps {
  isOpen?: boolean;
  onClose?: () => void;
  onToggle?: () => void;
  variant?: 'desktop' | 'mobile';
  isCollapsed?: boolean;
}

// Simple icon mapping
const iconMap: Record<string, React.ComponentType<any>> = {
  Home, User, Settings, Bot, LogOut, Shield, BarChart3, Users,
  FileText, Calendar, TrendingUp, Briefcase, DollarSign, BookOpen,
  Plus, Search, Flag, Folder
};

const UniversalSidebar: React.FC<UniversalSidebarProps> = ({
  isOpen = false,
  onClose,
  onToggle,
  variant = 'desktop',
  isCollapsed = false
}) => {
  const { user } = useAppSelector(state => state.auth);
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const location = useLocation();
  const { t } = useTranslation();
  const { isRTL, changeLanguage } = useLanguage();

  // ✅ SIMPLE REDUX INTEGRATION WITH ERROR HANDLING
  const { primaryRole, getSidebarNavigation } = useReduxRoles();

  // ✅ ARABIC TRANSLATION HELPER - COMPREHENSIVE TRANSLATIONS
  const getArabicTranslation = (key: string, isArabic: boolean) => {
    const translations: Record<string, { ar: string; en: string }> = {
      // Main Navigation
      'Dashboard': { ar: 'لوحة التحكم', en: 'Dashboard' },
      'Profile': { ar: 'الملف الشخصي', en: 'Profile' },
      'Settings': { ar: 'الإعدادات', en: 'Settings' },
      'AI Chat': { ar: 'محادثة الذكاء الاصطناعي', en: 'AI Chat' },

      // Admin & Management
      'Users': { ar: 'المستخدمون', en: 'Users' },
      'User Management': { ar: 'إدارة المستخدمين', en: 'User Management' },
      'System Management': { ar: 'إدارة النظام', en: 'System Management' },
      'Content Management': { ar: 'إدارة المحتوى', en: 'Content Management' },
      'AI Management': { ar: 'إدارة الذكاء الاصطناعي', en: 'AI Management' },

      // Analytics & Reports
      'Reports': { ar: 'التقارير', en: 'Reports' },
      'Analytics': { ar: 'التحليلات', en: 'Analytics' },
      'Market Analysis': { ar: 'تحليل السوق', en: 'Market Analysis' },

      // Communication
      'Messages': { ar: 'الرسائل', en: 'Messages' },
      'Notifications': { ar: 'الإشعارات', en: 'Notifications' },
      'Help': { ar: 'المساعدة', en: 'Help' },

      // Business & Entrepreneurship
      'Business Plans': { ar: 'خطط الأعمال', en: 'Business Plans' },
      'My Projects': { ar: 'مشاريعي', en: 'My Projects' },
      'Opportunities': { ar: 'الفرص', en: 'Opportunities' },
      'Portfolio': { ar: 'المحفظة', en: 'Portfolio' },

      // Mentorship & Learning
      'Mentorship': { ar: 'الإرشاد', en: 'Mentorship' },
      'My Mentees': { ar: 'المتدربون لدي', en: 'My Mentees' },
      'Sessions': { ar: 'الجلسات', en: 'Sessions' },

      // Funding & Investment
      'Funding': { ar: 'التمويل', en: 'Funding' },

      // Community & Events
      'Events': { ar: 'الفعاليات', en: 'Events' },
      'Community': { ar: 'المجتمع', en: 'Community' },
      'Resources': { ar: 'الموارد', en: 'Resources' },
      'Templates': { ar: 'القوالب', en: 'Templates' },
      'Incubator': { ar: 'الحاضنة', en: 'Incubator' },

      // Role translations
      'role_user': { ar: 'مستخدم', en: 'User' },
      'role_admin': { ar: 'مدير', en: 'Admin' },
      'role_super_admin': { ar: 'مدير عام', en: 'Super Admin' },
      'role_mentor': { ar: 'مرشد', en: 'Mentor' },
      'role_investor': { ar: 'مستثمر', en: 'Investor' },
      'role_entrepreneur': { ar: 'رائد أعمال', en: 'Entrepreneur' },
      'role_moderator': { ar: 'مشرف', en: 'Moderator' },

      // System & Misc
      'Logout': { ar: 'تسجيل الخروج', en: 'Logout' },
      'us': { ar: 'نحن', en: 'About Us' },
      'About Us': { ar: 'نحن', en: 'About Us' },
      'Contact': { ar: 'اتصل بنا', en: 'Contact' },
      'Support': { ar: 'الدعم', en: 'Support' }
    };

    const translation = translations[key];
    if (translation) {
      return isArabic ? translation.ar : translation.en;
    }

    // Enhanced fallback - try i18n first, then return original key
    const i18nResult = t(key);
    if (i18nResult && i18nResult !== key) {
      return i18nResult;
    }

    // Final fallback - return original key
    return key;
  };

  // ✅ SAFE NAVIGATION WITH IMPROVED TRANSLATIONS AND ERROR HANDLING
  const navItems = React.useMemo(() => {
    try {
      const items = getSidebarNavigation() || [];

      return items.map(item => ({
        ...item,
        name: getNavigationName(item, isRTL)
      }));
    } catch (error) {
      console.error('❌ Error getting sidebar navigation:', error);
      // Fallback navigation with proper translations
      return [
        {
          id: 'dashboard',
          name: isRTL ? 'لوحة التحكم' : 'Dashboard',
          nameAr: 'لوحة التحكم',
          path: '/dashboard',
          icon: 'Home',
          allowedRoles: ['admin', 'user'],
          category: 'main' as const
        },
        {
          id: 'ai-chat',
          name: isRTL ? 'محادثة الذكاء الاصطناعي' : 'AI Chat',
          nameAr: 'محادثة الذكاء الاصطناعي',
          path: '/ai-chat',
          icon: 'Bot',
          allowedRoles: ['admin', 'user'],
          category: 'ai' as const
        }
      ];
    }
  }, [getSidebarNavigation, isRTL, primaryRole]);

  // ✅ ENHANCED HANDLERS WITH KEYBOARD SUPPORT
  const handleNavigation = (path: string) => {
    navigate(path);
    if (variant === 'mobile' && onClose) {
      onClose();
    }
  };

  const handleLogout = async () => {
    try {
      await dispatch(logout()).unwrap();
      navigate('/login');
      if (variant === 'mobile' && onClose) {
        onClose();
      }
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  // ✅ KEYBOARD SUPPORT
  React.useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (variant === 'mobile' && isOpen && event.key === 'Escape') {
        onClose?.();
      }
    };

    if (variant === 'mobile' && isOpen) {
      document.addEventListener('keydown', handleKeyDown);
      return () => document.removeEventListener('keydown', handleKeyDown);
    }
  }, [variant, isOpen, onClose]);

  const isActive = (path: string) => location.pathname === path;

  // ✅ UNIFIED MODERN STYLING - No role-specific colors needed

  // ✅ MOBILE SIDEBAR WITH LOGIN PAGE STYLING
  if (variant === 'mobile') {
    return (
      <>
        {/* Enhanced backdrop with blur */}
        {isOpen && (
          <div
            className="fixed inset-0 bg-black/60 backdrop-blur-sm z-40 transition-all duration-300"
            onClick={onClose}
            onKeyDown={(e) => e.key === 'Escape' && onClose?.()}
          />
        )}
        {/* Mobile sidebar with gradient background like login page - IMPROVED WIDTH */}
        <div className={`fixed top-0 ${isRTL ? 'right-0' : 'left-0'} h-full w-72 bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 shadow-2xl transform transition-all duration-300 ease-in-out z-50 ${isRTL ? 'border-l border-purple-700/30 font-arabic' : 'border-r border-purple-700/30'} ${
          isOpen ? 'translate-x-0' : isRTL ? 'translate-x-full' : '-translate-x-full'
        }`}>
          <SidebarContent />
        </div>
      </>
    );
  }

  // ✅ DESKTOP SIDEBAR WITH LOGIN PAGE STYLING - FULL HEIGHT FIXED
  return (
    <div className={`${isCollapsed ? 'w-20' : 'w-72'} h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 shadow-2xl transition-all duration-300 ease-in-out flex-shrink-0 ${isRTL ? 'border-l border-purple-700/30 font-arabic' : 'border-r border-purple-700/30'} relative overflow-hidden`}>
      <SidebarContent />
      {/* ✅ COLLAPSED STATE INDICATOR */}
      {isCollapsed && (
        <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2">
          <div className="w-1 h-8 bg-gradient-to-b from-purple-400 to-blue-400 rounded-full opacity-50"></div>
        </div>
      )}
    </div>
  );

  function SidebarContent() {
    return (
      <div className="h-full flex flex-col min-h-0">
        {/* ✅ HEADER WITH LOGIN PAGE STYLING - IMPROVED LAYOUT */}
        <div className="p-4 border-b border-white/10 bg-white/5 backdrop-blur-sm">
          <div className={`flex items-center ${isCollapsed ? 'justify-center' : 'justify-between'} ${isRTL ? 'flex-row-reverse' : ''}`}>
            {!isCollapsed && (
              <div className={`${isRTL ? 'text-right' : 'text-left'} flex-1`}>
                <h1 className="text-lg font-bold text-white mb-1">
                  {isRTL ? 'ياسمين AI' : 'Yasmeen AI'}
                </h1>
                <p className="text-xs text-purple-200 capitalize">
                  {getArabicTranslation(`role_${primaryRole}`, isRTL) || primaryRole}
                </p>
              </div>
            )}
            {variant === 'mobile' && (
              <button
                onClick={onClose}
                className="p-2 hover:bg-white/20 rounded-lg transition-all duration-200 text-white"
                aria-label={t('sidebar.close', 'Close sidebar')}
              >
                <X className="w-5 h-5" />
              </button>
            )}
            {variant === 'desktop' && onToggle && (
              <button
                onClick={onToggle}
                className={`p-2 rounded-lg bg-white/10 hover:bg-white/20 transition-all duration-300 text-purple-200 hover:text-white backdrop-blur-sm border border-white/20 hover:border-white/40 ${isCollapsed ? '' : (isRTL ? 'ml-2' : 'mr-2')} flex-shrink-0`}
                title={isCollapsed ? t('sidebar.expand', 'Expand') : t('sidebar.collapse', 'Collapse')}
              >
                {isCollapsed ? <Menu className="w-4 h-4" /> : <X className="w-4 h-4" />}
              </button>
            )}
          </div>
        </div>

        {/* ✅ USER INFO WITH MODERN STYLING - IMPROVED FOR COLLAPSED STATE */}
        <div className="p-4 border-b border-white/10 bg-white/5">
          {isCollapsed ? (
            // Collapsed state - show only avatar with tooltip
            <div className="flex justify-center">
              <div
                className="w-10 h-10 bg-gradient-to-r from-purple-400 to-blue-400 rounded-full flex items-center justify-center shadow-lg ring-2 ring-white/20 cursor-pointer hover:ring-white/40 transition-all duration-300"
                title={`${user?.first_name} ${user?.last_name} (@${user?.username}) - ${getArabicTranslation(`role_${primaryRole}`, isRTL)}`}
              >
                <User className="w-5 h-5 text-white" />
              </div>
            </div>
          ) : (
            // Expanded state - show full user info
            <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : 'flex-row'}`}>
              <div className={`w-12 h-12 bg-gradient-to-r from-purple-400 to-blue-400 rounded-full flex items-center justify-center shadow-lg ring-2 ring-white/20 ${isRTL ? 'ml-3' : 'mr-3'}`}>
                <User className="w-7 h-7 text-white" />
              </div>
              <div className={`flex-1 ${isRTL ? 'text-right' : 'text-left'}`}>
                <p className="font-semibold text-white text-sm">
                  {user?.first_name} {user?.last_name}
                </p>
                <p className="text-xs text-purple-200">@{user?.username}</p>
                <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-white/10 text-purple-200 mt-1 backdrop-blur-sm ${isRTL ? 'flex-row-reverse' : 'flex-row'}`}>
                  <Shield className={`w-3 h-3 ${isRTL ? 'ml-1' : 'mr-1'}`} />
                  {getArabicTranslation(`role_${primaryRole}`, isRTL)}
                </span>
              </div>
            </div>
          )}
        </div>

        {/* ✅ NAVIGATION WITH MODERN STYLING AND ANIMATIONS - IMPROVED SPACING */}
        <nav className="flex-1 p-3 space-y-1 overflow-y-auto">
          {navItems.map((item) => {
            const active = isActive(item.path);

            return (
              <button
                key={item.id}
                onClick={() => handleNavigation(item.path)}
                className={`w-full flex items-center ${isCollapsed ? 'justify-center px-2' : 'px-3'} py-2.5 rounded-lg transition-all duration-300 group relative overflow-hidden ${
                  isRTL && !isCollapsed ? 'flex-row-reverse' : 'flex-row'
                } ${
                  active
                    ? 'bg-white/20 text-white shadow-lg backdrop-blur-sm border border-white/30'
                    : 'text-purple-200 hover:bg-white/10 hover:text-white'
                }`}
                title={isCollapsed ? item.name : undefined}
              >
                {/* Active indicator */}
                {active && (
                  <div className={`absolute ${isRTL ? 'right-0 rounded-l-full' : 'left-0 rounded-r-full'} top-0 bottom-0 w-1 bg-gradient-to-b from-purple-400 to-blue-400`} />
                )}

                {renderIcon(item.icon, {
                  className: `w-5 h-5 transition-all duration-300 group-hover:scale-110 ${
                    active ? 'text-white' : 'text-purple-300'
                  } ${isCollapsed ? '' : (isRTL ? 'ml-3' : 'mr-3')}`
                })}

                {!isCollapsed && (
                  <span className={`font-medium text-sm transition-all duration-300 flex-1 ${isRTL ? 'text-right' : 'text-left'}`}>
                    {item.name}
                  </span>
                )}

                {active && !isCollapsed && (
                  <div className={`${isRTL ? 'mr-auto' : 'ml-auto'} w-2 h-2 rounded-full bg-gradient-to-r from-purple-400 to-blue-400 animate-pulse`} />
                )}
              </button>
            );
          })}
        </nav>

        {/* ✅ LANGUAGE SWITCHER - IMPROVED FOR COLLAPSED STATE */}
        <div className="p-4 border-t border-white/10 bg-white/5">
          {isCollapsed ? (
            // Collapsed state - show only flag icon with tooltip
            <div className="flex justify-center">
              <button
                className="p-2 rounded-lg bg-white/10 hover:bg-white/20 transition-all duration-300 text-purple-200 hover:text-white backdrop-blur-sm border border-white/20 hover:border-white/40"
                title={isRTL ? 'تغيير اللغة' : 'Change Language'}
                onClick={() => {
                  // Simple language toggle for collapsed state
                  const newLang = isRTL ? 'en' : 'ar';
                  changeLanguage(newLang);
                }}
              >
                <span className="text-lg">{isRTL ? '🇸🇦' : '🇺🇸'}</span>
              </button>
            </div>
          ) : (
            // Expanded state - show full language switcher
            <LanguageSwitcher variant="compact" className="w-full" />
          )}
        </div>

        {/* ✅ LOGOUT WITH MODERN STYLING - IMPROVED FOR COLLAPSED STATE */}
        <div className="p-4 border-t border-white/10 bg-white/5">
          <button
            onClick={handleLogout}
            className={`w-full flex items-center ${isCollapsed ? 'justify-center px-2 py-2' : 'px-4 py-3'} text-red-300 hover:bg-red-500/20 hover:text-red-200 rounded-xl transition-all duration-300 group border border-transparent hover:border-red-400/30 backdrop-blur-sm ${isRTL && !isCollapsed ? 'flex-row-reverse' : 'flex-row'}`}
            title={getArabicTranslation('Logout', isRTL)}
          >
            <LogOut className={`w-5 h-5 transition-all duration-300 group-hover:scale-110 group-hover:rotate-12 ${isCollapsed ? '' : (isRTL ? 'ml-3' : 'mr-3')}`} />
            {!isCollapsed && (
              <span className={`font-medium text-sm flex-1 ${isRTL ? 'text-right' : 'text-left'}`}>
                {getArabicTranslation('Logout', isRTL)}
              </span>
            )}
          </button>
        </div>
      </div>
    );
  }
};

export default UniversalSidebar;
