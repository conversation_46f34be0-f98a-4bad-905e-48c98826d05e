/**
 * 🤖 AI SYSTEM VALIDATION TEST
 * Quick validation of AI system functionality
 */

import { test, expect } from '@playwright/test';

test.describe('🤖 AI System Validation', () => {
  
  test('🚀 AI routes should be accessible', async ({ page }) => {
    console.log('🧪 Testing AI route accessibility...');
    
    // Test main AI chat route
    await page.goto('/ai-chat');
    await page.waitForLoadState('load');
    
    // Check if page loads
    await expect(page.locator('body')).toBeVisible();
    
    // Check for AI-related content
    const pageContent = await page.textContent('body');
    const hasAIContent = pageContent?.toLowerCase().includes('ai') || 
                        pageContent?.toLowerCase().includes('chat') ||
                        pageContent?.toLowerCase().includes('yasmeen');
    
    console.log(`AI content detected: ${hasAIContent}`);
    console.log('✅ AI route accessibility test passed');
  });

  test('📱 AI should work on mobile', async ({ page }) => {
    console.log('🧪 Testing AI mobile compatibility...');
    
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    
    await page.goto('/ai-chat');
    await page.waitForLoadState('load');
    
    // Check if page is responsive
    await expect(page.locator('body')).toBeVisible();
    
    const bodyBox = await page.locator('body').boundingBox();
    expect(bodyBox?.width).toBeLessThanOrEqual(375);
    
    console.log('✅ AI mobile compatibility test passed');
  });

  test('⚡ AI should load quickly', async ({ page }) => {
    console.log('🧪 Testing AI performance...');
    
    const startTime = Date.now();
    
    await page.goto('/ai-chat');
    await page.waitForLoadState('load');
    
    const loadTime = Date.now() - startTime;
    
    // Should load within 5 seconds
    expect(loadTime).toBeLessThan(5000);
    
    console.log(`✅ AI loaded in ${loadTime}ms (under 5s)`);
  });

  test('🔄 AI navigation should work', async ({ page }) => {
    console.log('🧪 Testing AI navigation...');
    
    // Navigate to AI chat
    await page.goto('/ai-chat');
    await page.waitForLoadState('load');
    await expect(page.locator('body')).toBeVisible();
    
    // Navigate to business plans (may have AI integration)
    await page.goto('/business-plans');
    await page.waitForLoadState('load');
    await expect(page.locator('body')).toBeVisible();
    
    // Navigate back to AI
    await page.goto('/ai-chat');
    await page.waitForLoadState('load');
    await expect(page.locator('body')).toBeVisible();
    
    console.log('✅ AI navigation test passed');
  });

  test('🛡️ AI should handle errors gracefully', async ({ page }) => {
    console.log('🧪 Testing AI error handling...');
    
    // Listen for console errors
    const consoleErrors: string[] = [];
    page.on('console', msg => {
      if (msg.type() === 'error') {
        consoleErrors.push(msg.text());
      }
    });
    
    await page.goto('/ai-chat');
    await page.waitForLoadState('load');
    
    // Try invalid AI route
    await page.goto('/ai-chat/invalid');
    await page.waitForLoadState('load');
    
    // Should still have functional page
    await expect(page.locator('body')).toBeVisible();
    
    // Filter critical errors
    const criticalErrors = consoleErrors.filter(error =>
      !error.includes('404') &&
      !error.includes('favicon') &&
      !error.includes('manifest')
    );
    
    console.log(`Critical errors: ${criticalErrors.length}`);
    console.log('✅ AI error handling test passed');
  });

});
