/**
 * ✅ COMPREHENSIVE NAVIGATION CONFIGURATION
 * Centralized navigation management with proper translations and role-based access
 */

export interface NavigationItem {
  id: string;
  name: string;
  nameAr?: string; // Arabic translation
  path: string;
  icon: string;
  allowedRoles: string[];
  category: 'main' | 'ai' | 'business' | 'content' | 'system' | 'templates';
  badge?: string;
  children?: NavigationItem[];
}

/**
 * Base navigation items available to all authenticated users
 */
export const getBaseNavigation = (dashboardRoute: string): NavigationItem[] => [
  {
    id: 'dashboard',
    name: 'Dashboard',
    nameAr: 'لوحة التحكم',
    path: dashboardRoute,
    icon: 'Home',
    allowedRoles: ['user', 'admin', 'super_admin', 'mentor', 'investor', 'entrepreneur', 'moderator'],
    category: 'main'
  }
];

/**
 * Super Admin Navigation
 */
export const getSuperAdminNavigation = (dashboardRoute: string): NavigationItem[] => [
  ...getBaseNavigation(dashboardRoute),
  {
    id: 'ai-chat',
    name: '<PERSON> Chat',
    nameAr: 'محادثة الذكاء الاصطناعي',
    path: '/super-admin/ai-chat',
    icon: 'Bot',
    allowedRoles: ['super_admin'],
    category: 'ai'
  },
  {
    id: 'users',
    name: 'User Management',
    nameAr: 'إدارة المستخدمين',
    path: '/super-admin/users',
    icon: 'Users',
    allowedRoles: ['super_admin'],
    category: 'main'
  },
  {
    id: 'approvals',
    name: 'User Approvals',
    nameAr: 'موافقات المستخدمين',
    path: '/super-admin/approvals',
    icon: 'UserCheck',
    allowedRoles: ['super_admin'],
    category: 'main'
  },
  {
    id: 'system',
    name: 'System Management',
    nameAr: 'إدارة النظام',
    path: '/super-admin/system',
    icon: 'Settings',
    allowedRoles: ['super_admin'],
    category: 'system'
  },
  {
    id: 'analytics',
    name: 'Analytics',
    nameAr: 'التحليلات',
    path: '/super-admin/analytics',
    icon: 'BarChart3',
    allowedRoles: ['super_admin'],
    category: 'main'
  },
  {
    id: 'reports',
    name: 'Reports',
    nameAr: 'التقارير',
    path: '/super-admin/reports',
    icon: 'FileText',
    allowedRoles: ['super_admin'],
    category: 'main'
  },
  {
    id: 'settings',
    name: 'Settings',
    nameAr: 'الإعدادات',
    path: '/super-admin/settings',
    icon: 'Settings',
    allowedRoles: ['super_admin'],
    category: 'system'
  }
];

/**
 * Admin Navigation
 */
export const getAdminNavigation = (dashboardRoute: string): NavigationItem[] => [
  ...getBaseNavigation(dashboardRoute),
  {
    id: 'ai-chat',
    name: 'AI Chat',
    nameAr: 'محادثة الذكاء الاصطناعي',
    path: '/admin/ai-chat',
    icon: 'Bot',
    allowedRoles: ['admin'],
    category: 'ai'
  },
  {
    id: 'users',
    name: 'User Management',
    nameAr: 'إدارة المستخدمين',
    path: '/admin/users',
    icon: 'Users',
    allowedRoles: ['admin'],
    category: 'main'
  },
  {
    id: 'approvals',
    name: 'User Approvals',
    nameAr: 'موافقات المستخدمين',
    path: '/admin/approvals',
    icon: 'UserCheck',
    allowedRoles: ['admin'],
    category: 'main'
  },
  {
    id: 'content',
    name: 'Content Management',
    nameAr: 'إدارة المحتوى',
    path: '/admin/content',
    icon: 'FileText',
    allowedRoles: ['admin'],
    category: 'content'
  },
  {
    id: 'analytics',
    name: 'Analytics',
    nameAr: 'التحليلات',
    path: '/admin/analytics',
    icon: 'BarChart3',
    allowedRoles: ['admin'],
    category: 'main'
  },
  {
    id: 'business-plans',
    name: 'Business Plans',
    nameAr: 'خطط الأعمال',
    path: '/dashboard/business-plans',
    icon: 'Briefcase',
    allowedRoles: ['admin'],
    category: 'business'
  },
  {
    id: 'funding',
    name: 'Funding',
    nameAr: 'التمويل',
    path: '/dashboard/funding',
    icon: 'DollarSign',
    allowedRoles: ['admin'],
    category: 'business'
  },
  {
    id: 'settings',
    name: 'Settings',
    nameAr: 'الإعدادات',
    path: '/admin/settings',
    icon: 'Settings',
    allowedRoles: ['admin'],
    category: 'system'
  }
];

/**
 * Moderator Navigation
 */
export const getModeratorNavigation = (dashboardRoute: string): NavigationItem[] => [
  ...getBaseNavigation(dashboardRoute),
  {
    id: 'ai-chat',
    name: 'AI Chat',
    nameAr: 'محادثة الذكاء الاصطناعي',
    path: '/moderator/ai-chat',
    icon: 'Bot',
    allowedRoles: ['moderator'],
    category: 'ai'
  },
  {
    id: 'content-moderation',
    name: 'Content Moderation',
    nameAr: 'إدارة المحتوى',
    path: '/dashboard/content-moderation',
    icon: 'Shield',
    allowedRoles: ['moderator'],
    category: 'content'
  },
  {
    id: 'reports',
    name: 'Reports Management',
    nameAr: 'إدارة التقارير',
    path: '/dashboard/reports-management',
    icon: 'Flag',
    allowedRoles: ['moderator'],
    category: 'main'
  },
  {
    id: 'analytics',
    name: 'Analytics',
    nameAr: 'التحليلات',
    path: '/dashboard/analytics',
    icon: 'BarChart3',
    allowedRoles: ['moderator'],
    category: 'main'
  },
  {
    id: 'settings',
    name: 'Settings',
    nameAr: 'الإعدادات',
    path: '/moderator/settings',
    icon: 'Settings',
    allowedRoles: ['moderator'],
    category: 'system'
  }
];

/**
 * Mentor Navigation
 */
export const getMentorNavigation = (dashboardRoute: string): NavigationItem[] => [
  ...getBaseNavigation(dashboardRoute),
  {
    id: 'ai-chat',
    name: 'AI Chat',
    nameAr: 'محادثة الذكاء الاصطناعي',
    path: '/mentor/ai-chat',
    icon: 'Bot',
    allowedRoles: ['mentor'],
    category: 'ai'
  },
  {
    id: 'mentees',
    name: 'My Mentees',
    nameAr: 'المتدربين',
    path: '/mentor/mentees',
    icon: 'Users',
    allowedRoles: ['mentor'],
    category: 'main'
  },
  {
    id: 'sessions',
    name: 'Sessions',
    nameAr: 'الجلسات',
    path: '/mentor/sessions',
    icon: 'Calendar',
    allowedRoles: ['mentor'],
    category: 'main'
  },
  {
    id: 'mentorship',
    name: 'Mentorship',
    nameAr: 'الإرشاد',
    path: '/dashboard/mentorship',
    icon: 'BookOpen',
    allowedRoles: ['mentor'],
    category: 'main'
  },
  {
    id: 'resources',
    name: 'Resources',
    nameAr: 'الموارد',
    path: '/dashboard/resources',
    icon: 'Folder',
    allowedRoles: ['mentor'],
    category: 'content'
  },
  {
    id: 'settings',
    name: 'Settings',
    nameAr: 'الإعدادات',
    path: '/mentor/settings',
    icon: 'Settings',
    allowedRoles: ['mentor'],
    category: 'system'
  }
];

/**
 * Investor Navigation
 */
export const getInvestorNavigation = (dashboardRoute: string): NavigationItem[] => [
  ...getBaseNavigation(dashboardRoute),
  {
    id: 'ai-chat',
    name: 'AI Chat',
    nameAr: 'محادثة الذكاء الاصطناعي',
    path: '/investor/ai-chat',
    icon: 'Bot',
    allowedRoles: ['investor'],
    category: 'ai'
  },
  {
    id: 'portfolio',
    name: 'Portfolio',
    nameAr: 'المحفظة',
    path: '/investor/portfolio',
    icon: 'TrendingUp',
    allowedRoles: ['investor'],
    category: 'main'
  },
  {
    id: 'opportunities',
    name: 'Investment Opportunities',
    nameAr: 'فرص الاستثمار',
    path: '/investor/opportunities',
    icon: 'Search',
    allowedRoles: ['investor'],
    category: 'business'
  },
  {
    id: 'funding',
    name: 'Funding',
    nameAr: 'التمويل',
    path: '/dashboard/funding',
    icon: 'DollarSign',
    allowedRoles: ['investor'],
    category: 'business'
  },
  {
    id: 'analytics',
    name: 'Analytics',
    nameAr: 'التحليلات',
    path: '/dashboard/analytics',
    icon: 'BarChart3',
    allowedRoles: ['investor'],
    category: 'main'
  },
  {
    id: 'settings',
    name: 'Settings',
    nameAr: 'الإعدادات',
    path: '/investor/settings',
    icon: 'Settings',
    allowedRoles: ['investor'],
    category: 'system'
  }
];

/**
 * Entrepreneur Navigation
 */
export const getEntrepreneurNavigation = (dashboardRoute: string): NavigationItem[] => [
  ...getBaseNavigation(dashboardRoute),
  {
    id: 'ai-chat',
    name: 'AI Chat',
    nameAr: 'محادثة الذكاء الاصطناعي',
    path: '/entrepreneur/ai-chat',
    icon: 'Bot',
    allowedRoles: ['entrepreneur'],
    category: 'ai'
  },
  {
    id: 'business-ideas',
    name: 'Business Ideas',
    nameAr: 'الأفكار التجارية',
    path: '/dashboard/business-ideas',
    icon: 'Briefcase',
    allowedRoles: ['entrepreneur'],
    category: 'business'
  },
  {
    id: 'business-plans',
    name: 'Business Plans',
    nameAr: 'خطط الأعمال',
    path: '/dashboard/business-plans',
    icon: 'FileText',
    allowedRoles: ['entrepreneur'],
    category: 'business'
  },
  {
    id: 'funding',
    name: 'Funding',
    nameAr: 'التمويل',
    path: '/dashboard/funding',
    icon: 'DollarSign',
    allowedRoles: ['entrepreneur'],
    category: 'business'
  },
  {
    id: 'mentorship',
    name: 'Mentorship',
    nameAr: 'الإرشاد',
    path: '/dashboard/mentorship',
    icon: 'BookOpen',
    allowedRoles: ['entrepreneur'],
    category: 'main'
  },
  {
    id: 'templates',
    name: 'Templates',
    nameAr: 'القوالب',
    path: '/dashboard/template-library',
    icon: 'FileText',
    allowedRoles: ['entrepreneur'],
    category: 'templates'
  },
  {
    id: 'analytics',
    name: 'Analytics',
    nameAr: 'التحليلات',
    path: '/dashboard/analytics',
    icon: 'BarChart3',
    allowedRoles: ['entrepreneur'],
    category: 'main'
  },
  {
    id: 'settings',
    name: 'Settings',
    nameAr: 'الإعدادات',
    path: '/entrepreneur/settings',
    icon: 'Settings',
    allowedRoles: ['entrepreneur'],
    category: 'system'
  }
];

/**
 * User Navigation
 */
export const getUserNavigation = (dashboardRoute: string): NavigationItem[] => [
  ...getBaseNavigation(dashboardRoute),
  {
    id: 'ai-chat',
    name: 'AI Chat',
    nameAr: 'محادثة الذكاء الاصطناعي',
    path: '/user/ai-chat',
    icon: 'Bot',
    allowedRoles: ['user'],
    category: 'ai'
  },
  {
    id: 'profile',
    name: 'Profile',
    nameAr: 'الملف الشخصي',
    path: '/user/profile',
    icon: 'User',
    allowedRoles: ['user'],
    category: 'main'
  },
  {
    id: 'business-plans',
    name: 'Business Plans',
    nameAr: 'خطط الأعمال',
    path: '/dashboard/business-plans',
    icon: 'Briefcase',
    allowedRoles: ['user'],
    category: 'business'
  },
  {
    id: 'mentorship',
    name: 'Mentorship',
    nameAr: 'الإرشاد',
    path: '/dashboard/mentorship',
    icon: 'BookOpen',
    allowedRoles: ['user'],
    category: 'main'
  },
  {
    id: 'funding',
    name: 'Funding',
    nameAr: 'التمويل',
    path: '/dashboard/funding',
    icon: 'DollarSign',
    allowedRoles: ['user'],
    category: 'business'
  },
  {
    id: 'settings',
    name: 'Settings',
    nameAr: 'الإعدادات',
    path: '/user/settings',
    icon: 'Settings',
    allowedRoles: ['user'],
    category: 'system'
  }
];

/**
 * Get navigation items based on role
 */
export const getNavigationByRole = (role: string, dashboardRoute: string): NavigationItem[] => {
  switch (role) {
    case 'super_admin':
      return getSuperAdminNavigation(dashboardRoute);
    case 'admin':
      return getAdminNavigation(dashboardRoute);
    case 'moderator':
      return getModeratorNavigation(dashboardRoute);
    case 'mentor':
      return getMentorNavigation(dashboardRoute);
    case 'investor':
      return getInvestorNavigation(dashboardRoute);
    case 'entrepreneur':
      return getEntrepreneurNavigation(dashboardRoute);
    case 'user':
    default:
      return getUserNavigation(dashboardRoute);
  }
};

/**
 * Get translated navigation name
 */
export const getNavigationName = (item: NavigationItem, isRTL: boolean): string => {
  if (isRTL && item.nameAr) {
    return item.nameAr;
  }
  return item.name;
};
