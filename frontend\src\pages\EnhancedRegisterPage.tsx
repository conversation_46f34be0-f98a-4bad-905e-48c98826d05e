import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useLanguage } from '../components/LanguageProvider';
import { ArrowLeft, ArrowRight, CheckCircle, User, Users, FileText, Loader2, Check, X } from 'lucide-react';
import { api } from '../services/api';
import { useAppDispatch } from '../store/hooks';
import { setRegistrationData, completeRegistration } from '../store/registrationSlice';

// Inject custom CSS for dropdown options
const injectDropdownStyles = () => {
  const styleId = 'custom-dropdown-styles';
  if (!document.getElementById(styleId)) {
    const style = document.createElement('style');
    style.id = styleId;
    style.textContent = `
      .custom-select option {
        background-color: rgba(31, 41, 55, 0.95) !important;
        color: white !important;
        padding: 8px 12px !important;
      }
      .custom-select option:hover {
        background-color: rgba(147, 51, 234, 0.8) !important;
      }
      .custom-select option:checked {
        background-color: rgba(147, 51, 234, 1) !important;
      }
    `;
    document.head.appendChild(style);
  }
};

interface FormData {
  // Step 1: Basic Information
  firstName: string;
  lastName: string;
  username: string;
  email: string;
  password: string;
  passwordConfirm: string;
  phone: string;
  location: string;
  company: string;
  jobTitle: string;
  bio: string;
  website: string;
  linkedinUrl: string;

  // Step 2: Role Selection
  selectedRole: string;

  // Role-specific fields
  // Entrepreneur fields
  businessName: string;
  businessStage: string;
  industry: string;
  fundingNeeded: string;
  businessDescription: string;
  teamSize: string;
  supportNeeded: string;
  previousExperience: string;

  // Mentor fields
  expertise: string;
  experience: string;
  mentorshipAreas: string;
  availability: string;
  preferredCommunication: string;

  // Investor fields
  investmentRange: string;
  investmentStage: string;
  preferredIndustries: string;
  investmentCriteria: string;
  portfolioCompanies: string;

  // User fields
  interests: string;
  goals: string;

  // General fields (for all roles)
  portfolioUrl: string;
  motivation: string;
  qualifications: string;

  // Step 3: Terms
  agreeToTerms: boolean;
  agreeToPrivacy: boolean;
}

const EnhancedRegisterPage: React.FC = () => {
  const { language } = useLanguage();
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const isRTL = language === 'ar';
  
  const [currentStep, setCurrentStep] = useState(1);
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [success, setSuccess] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');
  const [countdown, setCountdown] = useState(5);
  const [validating, setValidating] = useState<Record<string, boolean>>({});
  const [validationStatus, setValidationStatus] = useState<Record<string, 'valid' | 'invalid' | 'checking'>>({});

  // ✅ FIX: Handle navigation when countdown reaches 0
  useEffect(() => {
    if (success && countdown === 0) {
      navigate('/register/success');
    }
  }, [countdown, success, navigate]);
  
  const [formData, setFormData] = useState<FormData>({
    firstName: '',
    lastName: '',
    username: '',
    email: '',
    password: '',
    passwordConfirm: '',
    phone: '',
    location: '',
    company: '',
    jobTitle: '',
    bio: '',
    website: '',
    linkedinUrl: '',
    selectedRole: '',
    // Entrepreneur fields
    businessName: '',
    businessStage: '',
    industry: '',
    fundingNeeded: '',
    businessDescription: '',
    teamSize: '',
    supportNeeded: '',
    previousExperience: '',
    // Mentor fields
    expertise: '',
    experience: '',
    mentorshipAreas: '',
    availability: '',
    preferredCommunication: '',
    // Investor fields
    investmentRange: '',
    investmentStage: '',
    preferredIndustries: '',
    investmentCriteria: '',
    portfolioCompanies: '',
    // User fields
    interests: '',
    goals: '',
    // General fields
    portfolioUrl: '',
    motivation: '',
    qualifications: '',
    agreeToTerms: false,
    agreeToPrivacy: false,
  });

  const totalSteps = 3;
  const progress = (currentStep / totalSteps) * 100;

  const roleOptions = [
    {
      key: 'entrepreneur',
      title: language === 'ar' ? 'رائد أعمال' : 'Entrepreneur',
      description: language === 'ar' ? 'لديك فكرة مشروع أو شركة ناشئة' : 'You have a business idea or startup',
      community: language === 'ar' ? 'انضم إلى مجتمع من رواد الأعمال المبدعين' : 'Join a community of innovative entrepreneurs',
      benefits: language === 'ar'
        ? ['الوصول إلى المرشدين', 'فرص التمويل', 'شبكة من رواد الأعمال', 'ورش عمل متخصصة']
        : ['Access to mentors', 'Funding opportunities', 'Entrepreneur network', 'Specialized workshops'],
      icon: '🚀',
      color: 'from-blue-500 to-purple-600'
    },
    {
      key: 'mentor',
      title: language === 'ar' ? 'مرشد' : 'Mentor',
      description: language === 'ar' ? 'تريد مساعدة رواد الأعمال الآخرين' : 'You want to help other entrepreneurs',
      community: language === 'ar' ? 'كن جزءاً من شبكة المرشدين المؤثرين' : 'Be part of an influential mentors network',
      benefits: language === 'ar'
        ? ['مشاركة الخبرات', 'بناء شبكة مهنية', 'تطوير المهارات', 'إحداث تأثير إيجابي']
        : ['Share expertise', 'Build professional network', 'Develop skills', 'Make positive impact'],
      icon: '🎯',
      color: 'from-green-500 to-teal-600'
    },
    {
      key: 'investor',
      title: language === 'ar' ? 'مستثمر' : 'Investor',
      description: language === 'ar' ? 'تبحث عن فرص استثمارية' : 'You are looking for investment opportunities',
      community: language === 'ar' ? 'اكتشف الفرص الاستثمارية الواعدة' : 'Discover promising investment opportunities',
      benefits: language === 'ar'
        ? ['مشاريع مدروسة', 'تنويع المحفظة', 'شبكة مستثمرين', 'عوائد مجزية']
        : ['Vetted projects', 'Portfolio diversification', 'Investor network', 'Attractive returns'],
      icon: '💎',
      color: 'from-yellow-500 to-orange-600'
    },
    {
      key: 'user',
      title: language === 'ar' ? 'مستخدم عادي' : 'Community Member',
      description: language === 'ar' ? 'تريد الاستفادة من المنصة والتعلم' : 'You want to learn and benefit from the platform',
      community: language === 'ar' ? 'تعلم وتطور مع مجتمع ريادة الأعمال' : 'Learn and grow with the entrepreneurship community',
      benefits: language === 'ar'
        ? ['محتوى تعليمي', 'فعاليات مجتمعية', 'شبكة تواصل', 'فرص تطوير']
        : ['Educational content', 'Community events', 'Networking', 'Development opportunities'],
      icon: '🌟',
      color: 'from-purple-500 to-pink-600'
    }
  ];

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }

    // Trigger real-time validation for specific fields
    if (field === 'username' && typeof value === 'string') {
      setValidationStatus(prev => ({ ...prev, username: 'checking' }));
      debouncedValidateUsername(value);
    } else if (field === 'email' && typeof value === 'string') {
      setValidationStatus(prev => ({ ...prev, email: 'checking' }));
      debouncedValidateEmail(value);
    } else if (field === 'password' || field === 'passwordConfirm') {
      // Validate password match in real-time
      const currentPassword = field === 'password' ? value : formData.password;
      const currentConfirm = field === 'passwordConfirm' ? value : formData.passwordConfirm;

      if (currentPassword && currentConfirm && currentPassword !== currentConfirm) {
        setErrors(prev => ({ ...prev, passwordConfirm: language === 'ar' ? 'كلمات المرور غير متطابقة' : 'Passwords do not match' }));
        setValidationStatus(prev => ({ ...prev, passwordConfirm: 'invalid' }));
      } else if (currentPassword && currentConfirm && currentPassword === currentConfirm) {
        setErrors(prev => ({ ...prev, passwordConfirm: '' }));
        setValidationStatus(prev => ({ ...prev, passwordConfirm: 'valid' }));
      }
    }
  };

  // Helper function to get input classes with error styling
  const getInputClasses = (fieldName: string) => {
    const baseClasses = "w-full px-4 py-3 bg-white/20 border rounded-lg focus:outline-none focus:ring-2 text-white placeholder-gray-400 transition-all duration-200";
    let borderClasses = "border-white/30 focus:ring-purple-500";

    if (errors[fieldName]) {
      borderClasses = "border-red-500 focus:ring-red-500 focus:border-red-500 shadow-red-500/20 shadow-lg";
    } else if (validationStatus[fieldName] === 'valid') {
      borderClasses = "border-green-500 focus:ring-green-500 focus:border-green-500 shadow-green-500/20 shadow-lg";
    } else if (validationStatus[fieldName] === 'checking') {
      borderClasses = "border-yellow-500 focus:ring-yellow-500 focus:border-yellow-500 shadow-yellow-500/20 shadow-lg";
    }

    return `${baseClasses} ${borderClasses}`;
  };

  // Enhanced error display component
  const ErrorMessage = ({ error, className = "" }: { error?: string; className?: string }) => {
    if (!error) return null;

    return (
      <div className={`flex items-center mt-2 text-red-400 text-sm animate-fadeIn ${className}`}>
        <X className="w-4 h-4 mr-2 flex-shrink-0" />
        <span>{error}</span>
      </div>
    );
  };

  // Success message component
  const SuccessMessage = ({ message, className = "" }: { message?: string; className?: string }) => {
    if (!message) return null;

    return (
      <div className={`flex items-center mt-2 text-green-400 text-sm animate-fadeIn ${className}`}>
        <Check className="w-4 h-4 mr-2 flex-shrink-0" />
        <span>{message}</span>
      </div>
    );
  };

  // Validation status message component
  const ValidationMessage = ({ field }: { field: string }) => {
    const status = validationStatus[field];
    const error = errors[field];

    if (error) {
      return <ErrorMessage error={error} />;
    }

    if (status === 'checking') {
      return (
        <div className="flex items-center mt-2 text-yellow-400 text-sm animate-pulse">
          <Loader2 className="w-4 h-4 mr-2 animate-spin" />
          <span>{language === 'ar' ? 'جاري التحقق...' : 'Checking...'}</span>
        </div>
      );
    }

    if (status === 'valid') {
      return <SuccessMessage message={language === 'ar' ? 'متاح' : 'Available'} />;
    }

    return null;
  };

  // Debounced validation function
  const debounceValidation = (func: Function, delay: number) => {
    let timeoutId: NodeJS.Timeout;
    return (...args: any[]) => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => func.apply(null, args), delay);
    };
  };

  // Real-time validation functions
  const validateUsername = async (username: string) => {
    if (!username || username.length < 3) {
      setValidationStatus(prev => ({ ...prev, username: 'invalid' }));
      setErrors(prev => ({ ...prev, username: language === 'ar' ? 'اسم المستخدم يجب أن يكون 3 أحرف على الأقل' : 'Username must be at least 3 characters' }));
      return false;
    }

    setValidationStatus(prev => ({ ...prev, username: 'checking' }));
    setValidating(prev => ({ ...prev, username: true }));

    try {
      const response = await api.post('/auth/check-username/', { username }) as any;
      if (response.available) {
        setValidationStatus(prev => ({ ...prev, username: 'valid' }));
        setErrors(prev => ({ ...prev, username: '' }));
        return true;
      } else {
        setValidationStatus(prev => ({ ...prev, username: 'invalid' }));
        setErrors(prev => ({ ...prev, username: language === 'ar' ? 'اسم المستخدم غير متاح' : 'Username is not available' }));
        return false;
      }
    } catch (error: any) {
      setValidationStatus(prev => ({ ...prev, username: 'invalid' }));
      setErrors(prev => ({ ...prev, username: language === 'ar' ? 'اسم المستخدم غير متاح' : 'Username is not available' }));
      return false;
    } finally {
      setValidating(prev => ({ ...prev, username: false }));
    }
  };

  const validateEmail = async (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!email || !emailRegex.test(email)) {
      setValidationStatus(prev => ({ ...prev, email: 'invalid' }));
      setErrors(prev => ({ ...prev, email: language === 'ar' ? 'البريد الإلكتروني غير صحيح' : 'Invalid email address' }));
      return false;
    }

    setValidationStatus(prev => ({ ...prev, email: 'checking' }));
    setValidating(prev => ({ ...prev, email: true }));

    try {
      const response = await api.post('/auth/check-email/', { email }) as any;
      if (response.available) {
        setValidationStatus(prev => ({ ...prev, email: 'valid' }));
        setErrors(prev => ({ ...prev, email: '' }));
        return true;
      } else {
        setValidationStatus(prev => ({ ...prev, email: 'invalid' }));
        setErrors(prev => ({ ...prev, email: language === 'ar' ? 'البريد الإلكتروني مستخدم بالفعل' : 'Email is already registered' }));
        return false;
      }
    } catch (error: any) {
      setValidationStatus(prev => ({ ...prev, email: 'invalid' }));
      setErrors(prev => ({ ...prev, email: language === 'ar' ? 'البريد الإلكتروني مستخدم بالفعل' : 'Email is already registered' }));
      return false;
    } finally {
      setValidating(prev => ({ ...prev, email: false }));
    }
  };

  // Create debounced versions
  const debouncedValidateUsername = debounceValidation(validateUsername, 800);
  const debouncedValidateEmail = debounceValidation(validateEmail, 800);

  // Step progress indicator component
  const StepProgressIndicator = () => {
    const steps = [
      { number: 1, title: language === 'ar' ? 'المعلومات الأساسية' : 'Basic Information' },
      { number: 2, title: language === 'ar' ? 'اختيار الدور' : 'Role Selection' },
      { number: 3, title: language === 'ar' ? 'الشروط والأحكام' : 'Terms & Conditions' }
    ];

    return (
      <div className="mb-8">
        <div className="flex items-center justify-between">
          {steps.map((step, index) => (
            <div key={step.number} className="flex items-center">
              <div className={`flex items-center justify-center w-10 h-10 rounded-full border-2 transition-all duration-300 ${
                currentStep >= step.number
                  ? 'bg-purple-600 border-purple-600 text-white'
                  : 'border-white/30 text-white/50'
              }`}>
                {currentStep > step.number ? (
                  <Check className="w-5 h-5" />
                ) : (
                  <span className="text-sm font-medium">{step.number}</span>
                )}
              </div>
              <div className={`ml-3 ${currentStep >= step.number ? 'text-white' : 'text-white/50'}`}>
                <p className="text-sm font-medium">{step.title}</p>
              </div>
              {index < steps.length - 1 && (
                <div className={`flex-1 h-0.5 mx-4 transition-all duration-300 ${
                  currentStep > step.number ? 'bg-purple-600' : 'bg-white/20'
                }`} />
              )}
            </div>
          ))}
        </div>
      </div>
    );
  };

  // Helper function to render validation icon
  const renderValidationIcon = (fieldName: string) => {
    const status = validationStatus[fieldName];
    if (!status) return null;

    switch (status) {
      case 'checking':
        return <Loader2 className="h-5 w-5 text-yellow-400 animate-spin" />;
      case 'valid':
        return <Check className="h-5 w-5 text-green-400" />;
      case 'invalid':
        return <X className="h-5 w-5 text-red-400" />;
      default:
        return null;
    }
  };

  const validateStep = (step: number): boolean => {
    const newErrors: Record<string, string> = {};

    switch (step) {
      case 1:
        if (!formData.firstName.trim()) {
          newErrors.firstName = language === 'ar' ? 'الاسم الأول مطلوب' : 'First name is required';
        }
        if (!formData.lastName.trim()) {
          newErrors.lastName = language === 'ar' ? 'الاسم الأخير مطلوب' : 'Last name is required';
        }
        if (!formData.username.trim()) {
          newErrors.username = language === 'ar' ? 'اسم المستخدم مطلوب' : 'Username is required';
        } else if (validationStatus.username === 'invalid') {
          // Username validation failed - keep existing error message
          if (errors.username) {
            newErrors.username = errors.username;
          } else {
            newErrors.username = language === 'ar' ? 'اسم المستخدم غير متاح' : 'Username is not available';
          }
        }
        // Remove blocking for 'checking' status - allow progression with warning
        // else if (validationStatus.username === 'checking') {
        //   newErrors.username = language === 'ar' ? 'جاري التحقق من اسم المستخدم...' : 'Checking username availability...';
        // } else if (validationStatus.username !== 'valid' && formData.username.trim()) {
        //   newErrors.username = language === 'ar' ? 'يرجى انتظار التحقق من اسم المستخدم' : 'Please wait for username validation';
        // }

        if (!formData.email.trim()) {
          newErrors.email = language === 'ar' ? 'البريد الإلكتروني مطلوب' : 'Email is required';
        } else if (validationStatus.email === 'invalid') {
          // Email validation failed - keep existing error message
          if (errors.email) {
            newErrors.email = errors.email;
          } else {
            newErrors.email = language === 'ar' ? 'البريد الإلكتروني مستخدم بالفعل' : 'Email is already registered';
          }
        }
        // Remove blocking for 'checking' status - allow progression with warning
        // else if (validationStatus.email === 'checking') {
        //   newErrors.email = language === 'ar' ? 'جاري التحقق من البريد الإلكتروني...' : 'Checking email availability...';
        // } else if (validationStatus.email !== 'valid' && formData.email.trim()) {
        //   newErrors.email = language === 'ar' ? 'يرجى انتظار التحقق من البريد الإلكتروني' : 'Please wait for email validation';
        // }

        if (!formData.password) {
          newErrors.password = language === 'ar' ? 'كلمة المرور مطلوبة' : 'Password is required';
        } else if (formData.password.length < 8) {
          newErrors.password = language === 'ar' ? 'كلمة المرور يجب أن تكون 8 أحرف على الأقل' : 'Password must be at least 8 characters';
        }

        if (!formData.passwordConfirm) {
          newErrors.passwordConfirm = language === 'ar' ? 'تأكيد كلمة المرور مطلوب' : 'Password confirmation is required';
        } else if (formData.password !== formData.passwordConfirm) {
          newErrors.passwordConfirm = language === 'ar' ? 'كلمات المرور غير متطابقة' : 'Passwords do not match';
        }

        // Make location optional to match backend serializer
        // if (!formData.location.trim()) {
        //   newErrors.location = language === 'ar' ? 'الموقع مطلوب' : 'Location is required';
        // }
        break;

      case 2:
        if (!formData.selectedRole) {
          newErrors.selectedRole = language === 'ar' ? 'يجب اختيار دور' : 'Please select a role';
        }

        // Validate portfolio URL if provided
        if (formData.portfolioUrl.trim()) {
          const urlPattern = /^https?:\/\/.+/;
          if (!urlPattern.test(formData.portfolioUrl.trim())) {
            newErrors.portfolioUrl = language === 'ar' ? 'يجب أن يكون الرابط صحيحاً ويبدأ بـ http:// أو https://' : 'URL must be valid and start with http:// or https://';
          }
        }

        // Role-specific field validation
        if (formData.selectedRole === 'entrepreneur') {
          if (!formData.businessName.trim()) {
            newErrors.businessName = language === 'ar' ? 'اسم المشروع مطلوب' : 'Business name is required';
          }
          if (!formData.businessStage) {
            newErrors.businessStage = language === 'ar' ? 'مرحلة المشروع مطلوبة' : 'Business stage is required';
          }
          if (!formData.industry.trim()) {
            newErrors.industry = language === 'ar' ? 'الصناعة مطلوبة' : 'Industry is required';
          }
          if (!formData.fundingNeeded) {
            newErrors.fundingNeeded = language === 'ar' ? 'مبلغ التمويل المطلوب مطلوب' : 'Funding needed is required';
          }
          if (!formData.teamSize) {
            newErrors.teamSize = language === 'ar' ? 'حجم الفريق مطلوب' : 'Team size is required';
          }
        }

        if (formData.selectedRole === 'mentor') {
          if (!formData.expertise.trim()) {
            newErrors.expertise = language === 'ar' ? 'الخبرة مطلوبة' : 'Expertise is required';
          }
          if (!formData.experience.trim()) {
            newErrors.experience = language === 'ar' ? 'التجربة مطلوبة' : 'Experience is required';
          }
          if (!formData.availability) {
            newErrors.availability = language === 'ar' ? 'التوفر مطلوب' : 'Availability is required';
          }
          if (!formData.preferredCommunication) {
            newErrors.preferredCommunication = language === 'ar' ? 'طريقة التواصل المفضلة مطلوبة' : 'Preferred communication is required';
          }
        }

        if (formData.selectedRole === 'investor') {
          if (!formData.investmentRange) {
            newErrors.investmentRange = language === 'ar' ? 'نطاق الاستثمار مطلوب' : 'Investment range is required';
          }
          if (!formData.investmentStage) {
            newErrors.investmentStage = language === 'ar' ? 'مرحلة الاستثمار مطلوبة' : 'Investment stage is required';
          }
          if (!formData.preferredIndustries.trim()) {
            newErrors.preferredIndustries = language === 'ar' ? 'الصناعات المفضلة مطلوبة' : 'Preferred industries is required';
          }
        }

        if (formData.selectedRole === 'community_member') {
          if (!formData.interests.trim()) {
            newErrors.interests = language === 'ar' ? 'الاهتمامات مطلوبة' : 'Interests are required';
          }
        }

        // Validate general required fields for all roles
        if (!formData.motivation.trim()) {
          newErrors.motivation = language === 'ar' ? 'دافع التقديم مطلوب' : 'Application motivation is required';
        }
        if (!formData.qualifications.trim()) {
          newErrors.qualifications = language === 'ar' ? 'المؤهلات والخبرات مطلوبة' : 'Qualifications and experience are required';
        }
        break;

      case 3:
        // Make role-specific fields optional to match backend serializer
        // Only validate terms and privacy agreements which are truly required

        // Validate terms and privacy
        if (!formData.agreeToTerms) {
          newErrors.agreeToTerms = language === 'ar' ? 'يجب الموافقة على الشروط والأحكام' : 'You must agree to the terms and conditions';
        }
        if (!formData.agreeToPrivacy) {
          newErrors.agreeToPrivacy = language === 'ar' ? 'يجب الموافقة على سياسة الخصوصية' : 'You must agree to the privacy policy';
        }
        break;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNext = () => {
    console.log('Attempting to go to next step from:', currentStep);
    console.log('Current form data:', formData);
    console.log('Current validation status:', validationStatus);

    if (validateStep(currentStep)) {
      console.log('Validation passed, moving to next step');
      setCurrentStep(prev => Math.min(prev + 1, totalSteps));
    } else {
      console.log('Validation failed, staying on current step');
    }
  };

  const handlePrevious = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1));
  };

  const handleSubmit = async () => {
    console.log('Starting form submission...');
    console.log('Current form data:', formData);

    // Final validation before submission
    if (!validateStep(currentStep)) {
      console.log('Validation failed for step:', currentStep);
      console.log('Current errors:', errors);
      return;
    }

    // Additional check for required fields
    if (!formData.agreeToTerms || !formData.agreeToPrivacy) {
      setErrors(prev => ({
        ...prev,
        submit: language === 'ar' ? 'يجب الموافقة على الشروط والأحكام وسياسة الخصوصية' : 'You must agree to the terms and privacy policy'
      }));
      return;
    }

    // Clear any previous errors
    setErrors({});
    setLoading(true);

    try {
      const registrationData = {
        username: formData.username,
        first_name: formData.firstName,
        last_name: formData.lastName,
        email: formData.email,
        password: formData.password,
        password_confirm: formData.passwordConfirm,
        phone: formData.phone,
        location: formData.location,
        bio: formData.bio,
        company: formData.company,
        job_title: formData.jobTitle,
        website: formData.website.trim() || null,
        linkedin_url: formData.linkedinUrl.trim() || null,
        language: language,
        selected_role: formData.selectedRole,
        // Role-specific fields
        business_name: formData.businessName,
        business_stage: formData.businessStage,
        industry: formData.industry,
        funding_needed: formData.fundingNeeded,
        business_description: formData.businessDescription,
        team_size: formData.teamSize,
        support_needed: formData.supportNeeded,
        previous_experience: formData.previousExperience,
        expertise: formData.expertise,
        experience: formData.experience,
        mentorship_areas: formData.mentorshipAreas,
        availability: formData.availability,
        preferred_communication: formData.preferredCommunication,
        investment_range: formData.investmentRange,
        investment_stage: formData.investmentStage,
        preferred_industries: formData.preferredIndustries,
        investment_criteria: formData.investmentCriteria,
        portfolio_companies: formData.portfolioCompanies,
        interests: formData.interests,
        goals: formData.goals,
        portfolio_url: formData.portfolioUrl.trim() || null,
        motivation: formData.motivation,
        qualifications: formData.qualifications
      };

      console.log('🔍 DEBUG: Portfolio URL value:', formData.portfolioUrl);
      console.log('🔍 DEBUG: Portfolio URL after trim:', formData.portfolioUrl.trim());
      console.log('🔍 DEBUG: Portfolio URL final value:', formData.portfolioUrl.trim() || null);
      console.log('Sending registration data:', registrationData);
      const response = await api.post('/auth/register-enhanced/', registrationData) as any;
      console.log('Registration response:', response);
      console.log('Response status:', response.status);
      console.log('Response data:', response.data);

      if (response && (response.status === 201 || response.user || response.message)) {
        console.log('Registration successful!');
        console.log('Setting success to true...');
        setSuccess(true);
        setErrors({}); // Clear any previous errors
        setCountdown(5);
        console.log('Success state set, countdown started');

        // Check if user needs approval based on role and backend response
        const backendRequiresApproval = response.requires_approval || false;
        const needsApproval = backendRequiresApproval;

        // Store registration data in Redux
        dispatch(setRegistrationData({
          firstName: formData.firstName,
          lastName: formData.lastName,
          username: formData.username,
          email: formData.email,
          selectedRole: formData.selectedRole,
          phone: formData.phone,
          location: formData.location,
          company: formData.company,
          jobTitle: formData.jobTitle,
          bio: formData.bio,
          website: formData.website,
          linkedinUrl: formData.linkedinUrl,
          businessName: formData.businessName,
          businessStage: formData.businessStage,
          industry: formData.industry,
          fundingNeeded: formData.fundingNeeded,
          businessDescription: formData.businessDescription,
          teamSize: formData.teamSize,
          supportNeeded: formData.supportNeeded,
          previousExperience: formData.previousExperience,
          expertise: formData.expertise,
          experience: formData.experience,
          mentorshipAreas: formData.mentorshipAreas,
          availability: formData.availability,
          preferredCommunication: formData.preferredCommunication,
          investmentRange: formData.investmentRange,
          investmentStage: formData.investmentStage,
          preferredIndustries: formData.preferredIndustries,
          investmentCriteria: formData.investmentCriteria,
          portfolioCompanies: formData.portfolioCompanies,
          interests: formData.interests,
          goals: formData.goals,
          portfolioUrl: formData.portfolioUrl,
        }));

        // Mark registration as completed
        dispatch(completeRegistration({
          requiresApproval: needsApproval
        }));

        // ✅ FIX: Start countdown without navigation in render
        const countdownInterval = setInterval(() => {
          setCountdown(prev => {
            if (prev <= 1) {
              clearInterval(countdownInterval);
              return 0; // Navigation handled by useEffect
            }
            return prev - 1;
          });
        }, 1000);

        // Use backend message or create appropriate message based on approval status
        const message = response.message || (needsApproval
          ? (language === 'ar'
              ? 'تم إنشاء الحساب بنجاح! سيتم مراجعة طلبك قريباً.'
              : 'Account created successfully! Your application will be reviewed soon.')
          : (language === 'ar'
              ? 'تم إنشاء الحساب بنجاح! يمكنك الآن تسجيل الدخول.'
              : 'Account created successfully! You can now login.'));

        console.log('Setting success message:', message);
        setSuccessMessage(message);
        console.log('Success message set, should display now');
      }
    } catch (error: any) {
      console.error('Registration error:', error);
      console.error('Error response:', error.response);

      // Handle validation errors from backend
      const responseData = error.response?.data;
      let errorMessages: any = {};

      if (responseData?.errors) {
        console.log('Backend validation errors:', responseData.errors);
        // Parse field-specific validation errors
        Object.keys(responseData.errors).forEach(field => {
          const fieldErrors = responseData.errors[field];
          if (Array.isArray(fieldErrors)) {
            errorMessages[field] = fieldErrors.join(', ');
          } else {
            errorMessages[field] = fieldErrors;
          }
        });
      }

      // Add general error message with more specific information
      if (error.response?.status === 400) {
        errorMessages.submit = responseData?.message ||
                              (language === 'ar' ? 'يرجى التحقق من البيانات المدخلة' : 'Please check your input data');
      } else if (error.response?.status === 500) {
        errorMessages.submit = language === 'ar' ? 'خطأ في الخادم، يرجى المحاولة مرة أخرى' : 'Server error, please try again';
      } else {
        errorMessages.submit = responseData?.message ||
                              (language === 'ar' ? 'حدث خطأ أثناء التسجيل' : 'Registration failed');
      }

      console.log('Setting error messages:', errorMessages);
      setErrors(errorMessages);
    } finally {
      setLoading(false);
    }
  };

  // Reusable styled select component
  const StyledSelect = ({
    value,
    onChange,
    options,
    placeholder,
    error = null
  }: {
    value: string;
    onChange: (value: string) => void;
    options: { value: string; label: string }[];
    placeholder: string;
    error?: string | null;
  }) => {
    // Inject custom styles on first render
    React.useEffect(() => {
      injectDropdownStyles();
    }, []);

    return (
      <div>
        <div className="relative">
          <select
            value={value}
            onChange={(e) => onChange(e.target.value)}
            className="custom-select w-full px-4 py-3 bg-gradient-to-r from-white/20 to-white/10 border border-white/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-400 text-white appearance-none cursor-pointer transition-all duration-300 hover:bg-white/25 hover:border-purple-300"
            dir={language === 'ar' ? 'rtl' : 'ltr'}
            style={{
              backgroundImage: `url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%23a855f7' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e")`,
              backgroundPosition: language === 'ar' ? 'left 0.75rem center' : 'right 0.75rem center',
              backgroundRepeat: 'no-repeat',
              backgroundSize: '1.5em 1.5em',
              paddingRight: language === 'ar' ? '1rem' : '2.5rem',
              paddingLeft: language === 'ar' ? '2.5rem' : '1rem',
              colorScheme: 'dark'
            }}
          >
            <option value="">{placeholder}</option>
            {options.map((option) => (
              <option
                key={option.value}
                value={option.value}
              >
                {option.label}
              </option>
            ))}
          </select>
        </div>
        {error && <p className="text-red-400 text-sm mt-1">{error}</p>}
      </div>
    );
  };

  const renderRoleSpecificFields = () => {
    switch (formData.selectedRole) {
      case 'entrepreneur':
        return (
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  {language === 'ar' ? 'اسم المشروع' : 'Business Name'} <span className="text-red-400">*</span>
                </label>
                <input
                  type="text"
                  value={formData.businessName}
                  onChange={(e) => handleInputChange('businessName', e.target.value)}
                  className={getInputClasses('businessName')}
                  placeholder={language === 'ar' ? 'أدخل اسم مشروعك' : 'Enter your business name'}
                  dir={language === 'ar' ? 'rtl' : 'ltr'}
                />
                {errors.businessName && <p className="text-red-400 text-sm mt-1">{errors.businessName}</p>}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  {language === 'ar' ? 'مرحلة المشروع' : 'Business Stage'} <span className="text-red-400">*</span>
                </label>
                <StyledSelect
                  value={formData.businessStage}
                  onChange={(value) => handleInputChange('businessStage', value)}
                  options={[
                    { value: 'idea', label: language === 'ar' ? 'فكرة' : 'Idea' },
                    { value: 'prototype', label: language === 'ar' ? 'نموذج أولي' : 'Prototype' },
                    { value: 'mvp', label: language === 'ar' ? 'منتج أولي' : 'MVP' },
                    { value: 'growth', label: language === 'ar' ? 'نمو' : 'Growth' },
                    { value: 'established', label: language === 'ar' ? 'راسخ' : 'Established' }
                  ]}
                  placeholder={language === 'ar' ? 'اختر مرحلة المشروع' : 'Select business stage'}
                  error={errors.businessStage}
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                {language === 'ar' ? 'القطاع' : 'Industry'} <span className="text-red-400">*</span>
              </label>
              <input
                type="text"
                value={formData.industry}
                onChange={(e) => handleInputChange('industry', e.target.value)}
                className="w-full px-4 py-3 bg-white/20 border border-white/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white placeholder-gray-400"
                placeholder={language === 'ar' ? 'مثل: التكنولوجيا، التجارة الإلكترونية، الصحة' : 'e.g., Technology, E-commerce, Healthcare'}
                dir={language === 'ar' ? 'rtl' : 'ltr'}
              />
              {errors.industry && <p className="text-red-400 text-sm mt-1">{errors.industry}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                {language === 'ar' ? 'التمويل المطلوب' : 'Funding Needed'}
              </label>
              <StyledSelect
                value={formData.fundingNeeded}
                onChange={(value) => handleInputChange('fundingNeeded', value)}
                options={[
                  { value: 'none', label: language === 'ar' ? 'لا أحتاج تمويل' : 'No funding needed' },
                  { value: 'under-50k', label: language === 'ar' ? 'أقل من 50 ألف' : 'Under $50K' },
                  { value: '50k-250k', label: language === 'ar' ? '50-250 ألف' : '$50K - $250K' },
                  { value: '250k-1m', label: language === 'ar' ? '250 ألف - مليون' : '$250K - $1M' },
                  { value: 'over-1m', label: language === 'ar' ? 'أكثر من مليون' : 'Over $1M' }
                ]}
                placeholder={language === 'ar' ? 'اختر مبلغ التمويل' : 'Select funding amount'}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  {language === 'ar' ? 'حجم الفريق' : 'Team Size'}
                </label>
                <StyledSelect
                  value={formData.teamSize}
                  onChange={(value) => handleInputChange('teamSize', value)}
                  options={[
                    { value: 'solo', label: language === 'ar' ? 'مؤسس واحد' : 'Solo Founder' },
                    { value: '2-3', label: language === 'ar' ? '2-3 أشخاص' : '2-3 People' },
                    { value: '4-10', label: language === 'ar' ? '4-10 أشخاص' : '4-10 People' },
                    { value: '11-25', label: language === 'ar' ? '11-25 شخص' : '11-25 People' },
                    { value: '25+', label: language === 'ar' ? 'أكثر من 25 شخص' : 'More than 25 People' }
                  ]}
                  placeholder={language === 'ar' ? 'اختر حجم الفريق' : 'Select team size'}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  {language === 'ar' ? 'الخبرة السابقة' : 'Previous Experience'}
                </label>
                <input
                  type="text"
                  value={formData.previousExperience}
                  onChange={(e) => handleInputChange('previousExperience', e.target.value)}
                  className="w-full px-4 py-3 bg-white/20 border border-white/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white placeholder-gray-400"
                  placeholder={language === 'ar' ? 'خبرتك السابقة في ريادة الأعمال' : 'Your previous entrepreneurship experience'}
                  dir={language === 'ar' ? 'rtl' : 'ltr'}
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                {language === 'ar' ? 'الدعم المطلوب' : 'Support Needed'}
              </label>
              <textarea
                value={formData.supportNeeded}
                onChange={(e) => handleInputChange('supportNeeded', e.target.value)}
                rows={2}
                className="w-full px-4 py-3 bg-white/20 border border-white/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white placeholder-gray-400 resize-none"
                placeholder={language === 'ar' ? 'ما نوع الدعم الذي تحتاجه؟' : 'What type of support do you need?'}
                dir={language === 'ar' ? 'rtl' : 'ltr'}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                {language === 'ar' ? 'وصف المشروع' : 'Business Description'}
              </label>
              <textarea
                value={formData.businessDescription}
                onChange={(e) => handleInputChange('businessDescription', e.target.value)}
                rows={3}
                className="w-full px-4 py-3 bg-white/20 border border-white/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white placeholder-gray-400 resize-none"
                placeholder={language === 'ar' ? 'اشرح فكرة مشروعك بإيجاز' : 'Briefly describe your business idea'}
                dir={language === 'ar' ? 'rtl' : 'ltr'}
              />
            </div>
          </div>
        );

      case 'mentor':
        return (
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  {language === 'ar' ? 'مجال الخبرة' : 'Expertise Area'} <span className="text-red-400">*</span>
                </label>
                <input
                  type="text"
                  value={formData.expertise}
                  onChange={(e) => handleInputChange('expertise', e.target.value)}
                  className="w-full px-4 py-3 bg-white/20 border border-white/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white placeholder-gray-400"
                  placeholder={language === 'ar' ? 'مثل: التسويق، التكنولوجيا، المبيعات' : 'e.g., Marketing, Technology, Sales'}
                  dir={language === 'ar' ? 'rtl' : 'ltr'}
                />
                {errors.expertise && <p className="text-red-400 text-sm mt-1">{errors.expertise}</p>}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  {language === 'ar' ? 'سنوات الخبرة' : 'Years of Experience'} <span className="text-red-400">*</span>
                </label>
                <StyledSelect
                  value={formData.experience}
                  onChange={(value) => handleInputChange('experience', value)}
                  options={[
                    { value: '1-3', label: language === 'ar' ? '1-3 سنوات' : '1-3 years' },
                    { value: '4-7', label: language === 'ar' ? '4-7 سنوات' : '4-7 years' },
                    { value: '8-15', label: language === 'ar' ? '8-15 سنة' : '8-15 years' },
                    { value: '15+', label: language === 'ar' ? 'أكثر من 15 سنة' : '15+ years' }
                  ]}
                  placeholder={language === 'ar' ? 'اختر سنوات الخبرة' : 'Select years of experience'}
                  error={errors.experience}
                />
                {errors.experience && <p className="text-red-400 text-sm mt-1">{errors.experience}</p>}
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                {language === 'ar' ? 'مجالات الإرشاد' : 'Mentorship Areas'}
              </label>
              <textarea
                value={formData.mentorshipAreas}
                onChange={(e) => handleInputChange('mentorshipAreas', e.target.value)}
                rows={3}
                className="w-full px-4 py-3 bg-white/20 border border-white/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white placeholder-gray-400 resize-none"
                placeholder={language === 'ar' ? 'اذكر المجالات التي يمكنك تقديم الإرشاد فيها' : 'List the areas where you can provide mentorship'}
                dir={language === 'ar' ? 'rtl' : 'ltr'}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  {language === 'ar' ? 'التوفر' : 'Availability'}
                </label>
                <StyledSelect
                  value={formData.availability}
                  onChange={(value) => handleInputChange('availability', value)}
                  options={[
                    { value: '1-2-hours', label: language === 'ar' ? '1-2 ساعة أسبوعياً' : '1-2 hours per week' },
                    { value: '3-5-hours', label: language === 'ar' ? '3-5 ساعات أسبوعياً' : '3-5 hours per week' },
                    { value: '5+-hours', label: language === 'ar' ? 'أكثر من 5 ساعات أسبوعياً' : '5+ hours per week' },
                    { value: 'flexible', label: language === 'ar' ? 'مرن' : 'Flexible' }
                  ]}
                  placeholder={language === 'ar' ? 'اختر مدى توفرك' : 'Select your availability'}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  {language === 'ar' ? 'طريقة التواصل المفضلة' : 'Preferred Communication'}
                </label>
                <StyledSelect
                  value={formData.preferredCommunication}
                  onChange={(value) => handleInputChange('preferredCommunication', value)}
                  options={[
                    { value: 'video-calls', label: language === 'ar' ? 'مكالمات فيديو' : 'Video Calls' },
                    { value: 'phone-calls', label: language === 'ar' ? 'مكالمات هاتفية' : 'Phone Calls' },
                    { value: 'messaging', label: language === 'ar' ? 'الرسائل النصية' : 'Text Messaging' },
                    { value: 'email', label: language === 'ar' ? 'البريد الإلكتروني' : 'Email' },
                    { value: 'in-person', label: language === 'ar' ? 'لقاءات شخصية' : 'In-Person Meetings' }
                  ]}
                  placeholder={language === 'ar' ? 'اختر طريقة التواصل' : 'Select communication method'}
                />
              </div>
            </div>
          </div>
        );

      case 'investor':
        return (
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  {language === 'ar' ? 'نطاق الاستثمار' : 'Investment Range'} <span className="text-red-400">*</span>
                </label>
                <StyledSelect
                  value={formData.investmentRange}
                  onChange={(value) => handleInputChange('investmentRange', value)}
                  options={[
                    { value: '10k-50k', label: language === 'ar' ? '10-50 ألف' : '$10K - $50K' },
                    { value: '50k-250k', label: language === 'ar' ? '50-250 ألف' : '$50K - $250K' },
                    { value: '250k-1m', label: language === 'ar' ? '250 ألف - مليون' : '$250K - $1M' },
                    { value: '1m-5m', label: language === 'ar' ? '1-5 مليون' : '$1M - $5M' },
                    { value: 'over-5m', label: language === 'ar' ? 'أكثر من 5 مليون' : 'Over $5M' }
                  ]}
                  placeholder={language === 'ar' ? 'اختر نطاق الاستثمار' : 'Select investment range'}
                  error={errors.investmentRange}
                />
                {errors.investmentRange && <p className="text-red-400 text-sm mt-1">{errors.investmentRange}</p>}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  {language === 'ar' ? 'مرحلة الاستثمار' : 'Investment Stage'} <span className="text-red-400">*</span>
                </label>
                <StyledSelect
                  value={formData.investmentStage}
                  onChange={(value) => handleInputChange('investmentStage', value)}
                  options={[
                    { value: 'pre-seed', label: language === 'ar' ? 'ما قبل البذرة' : 'Pre-seed' },
                    { value: 'seed', label: language === 'ar' ? 'البذرة' : 'Seed' },
                    { value: 'series-a', label: language === 'ar' ? 'الجولة أ' : 'Series A' },
                    { value: 'series-b', label: language === 'ar' ? 'الجولة ب' : 'Series B' },
                    { value: 'growth', label: language === 'ar' ? 'النمو' : 'Growth' }
                  ]}
                  placeholder={language === 'ar' ? 'اختر مرحلة الاستثمار' : 'Select investment stage'}
                  error={errors.investmentStage}
                />
                {errors.investmentStage && <p className="text-red-400 text-sm mt-1">{errors.investmentStage}</p>}
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                {language === 'ar' ? 'القطاعات المفضلة' : 'Preferred Industries'}
              </label>
              <textarea
                value={formData.preferredIndustries}
                onChange={(e) => handleInputChange('preferredIndustries', e.target.value)}
                rows={3}
                className="w-full px-4 py-3 bg-white/20 border border-white/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white placeholder-gray-400 resize-none"
                placeholder={language === 'ar' ? 'اذكر القطاعات التي تفضل الاستثمار فيها' : 'List the industries you prefer to invest in'}
                dir={language === 'ar' ? 'rtl' : 'ltr'}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                {language === 'ar' ? 'معايير الاستثمار' : 'Investment Criteria'}
              </label>
              <textarea
                value={formData.investmentCriteria}
                onChange={(e) => handleInputChange('investmentCriteria', e.target.value)}
                rows={3}
                className="w-full px-4 py-3 bg-white/20 border border-white/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white placeholder-gray-400 resize-none"
                placeholder={language === 'ar' ? 'اذكر المعايير التي تبحث عنها في الاستثمار' : 'Describe what you look for in an investment'}
                dir={language === 'ar' ? 'rtl' : 'ltr'}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                {language === 'ar' ? 'الشركات في المحفظة' : 'Portfolio Companies'}
              </label>
              <textarea
                value={formData.portfolioCompanies}
                onChange={(e) => handleInputChange('portfolioCompanies', e.target.value)}
                rows={2}
                className="w-full px-4 py-3 bg-white/20 border border-white/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white placeholder-gray-400 resize-none"
                placeholder={language === 'ar' ? 'اذكر بعض الشركات في محفظتك الاستثمارية' : 'List some companies in your investment portfolio'}
                dir={language === 'ar' ? 'rtl' : 'ltr'}
              />
            </div>
          </div>
        );

      case 'user':
        return (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                {language === 'ar' ? 'الاهتمامات' : 'Interests'} <span className="text-red-400">*</span>
              </label>
              <textarea
                value={formData.interests}
                onChange={(e) => handleInputChange('interests', e.target.value)}
                rows={3}
                className="w-full px-4 py-3 bg-white/20 border border-white/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white placeholder-gray-400 resize-none"
                placeholder={language === 'ar' ? 'اذكر اهتماماتك في مجال ريادة الأعمال' : 'Describe your interests in entrepreneurship'}
                dir={language === 'ar' ? 'rtl' : 'ltr'}
              />
              {errors.interests && <p className="text-red-400 text-sm mt-1">{errors.interests}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                {language === 'ar' ? 'الأهداف' : 'Goals'}
              </label>
              <textarea
                value={formData.goals}
                onChange={(e) => handleInputChange('goals', e.target.value)}
                rows={3}
                className="w-full px-4 py-3 bg-white/20 border border-white/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white placeholder-gray-400 resize-none"
                placeholder={language === 'ar' ? 'ما الذي تريد تحقيقه من خلال المنصة؟' : 'What do you want to achieve through the platform?'}
                dir={language === 'ar' ? 'rtl' : 'ltr'}
              />
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  const renderGeneralFields = () => {
    return (
      <div className="mt-6 pt-6 border-t border-white/20 space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            {language === 'ar' ? 'رابط المحفظة أو الأعمال' : 'Portfolio/Work URL'}
            <span className="text-gray-400 text-xs ml-1">({language === 'ar' ? 'اختياري' : 'Optional'})</span>
          </label>
          <input
            type="url"
            value={formData.portfolioUrl}
            onChange={(e) => handleInputChange('portfolioUrl', e.target.value)}
            className={`w-full px-4 py-3 bg-white/20 border rounded-lg focus:outline-none focus:ring-2 text-white placeholder-gray-400 ${
              errors.portfolioUrl ? 'border-red-500 focus:ring-red-500' : 'border-white/30 focus:ring-purple-500'
            }`}
            placeholder={language === 'ar' ? 'https://example.com/portfolio' : 'https://example.com/portfolio'}
            dir="ltr"
          />
          {errors.portfolioUrl && (
            <div className="flex items-center mt-1 text-red-400 text-sm">
              <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
              {errors.portfolioUrl}
            </div>
          )}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              {language === 'ar' ? 'الموقع الإلكتروني' : 'Website'}
              <span className="text-gray-400 text-xs ml-1">({language === 'ar' ? 'اختياري' : 'Optional'})</span>
            </label>
            <input
              type="url"
              value={formData.website || ''}
              onChange={(e) => handleInputChange('website', e.target.value)}
              className="w-full px-4 py-3 bg-white/20 border border-white/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white placeholder-gray-400"
              placeholder={language === 'ar' ? 'https://example.com' : 'https://example.com'}
              dir="ltr"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              {language === 'ar' ? 'لينكد إن' : 'LinkedIn'}
              <span className="text-gray-400 text-xs ml-1">({language === 'ar' ? 'اختياري' : 'Optional'})</span>
            </label>
            <input
              type="url"
              value={formData.linkedinUrl || ''}
              onChange={(e) => handleInputChange('linkedinUrl', e.target.value)}
              className="w-full px-4 py-3 bg-white/20 border border-white/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white placeholder-gray-400"
              placeholder={language === 'ar' ? 'https://linkedin.com/in/username' : 'https://linkedin.com/in/username'}
              dir="ltr"
            />
          </div>
        </div>

        {/* Motivation and Qualifications */}
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              {language === 'ar' ? 'دافع التقديم' : 'Application Motivation'}
              <span className="text-red-400 ml-1">*</span>
            </label>
            <textarea
              value={formData.motivation}
              onChange={(e) => handleInputChange('motivation', e.target.value)}
              className={`w-full px-4 py-3 bg-white/20 border rounded-lg focus:outline-none focus:ring-2 text-white placeholder-gray-400 resize-none ${
                errors.motivation ? 'border-red-500 focus:ring-red-500' : 'border-white/30 focus:ring-purple-500'
              }`}
              placeholder={language === 'ar' ? 'اشرح سبب اهتمامك بهذا الدور وما تأمل في تحقيقه...' : 'Explain why you are interested in this role and what you hope to achieve...'}
              rows={3}
              dir={language === 'ar' ? 'rtl' : 'ltr'}
            />
            {errors.motivation && (
              <div className="flex items-center mt-1 text-red-400 text-sm">
                <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
                {errors.motivation}
              </div>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              {language === 'ar' ? 'المؤهلات والخبرات' : 'Qualifications & Experience'}
              <span className="text-red-400 ml-1">*</span>
            </label>
            <textarea
              value={formData.qualifications}
              onChange={(e) => handleInputChange('qualifications', e.target.value)}
              className={`w-full px-4 py-3 bg-white/20 border rounded-lg focus:outline-none focus:ring-2 text-white placeholder-gray-400 resize-none ${
                errors.qualifications ? 'border-red-500 focus:ring-red-500' : 'border-white/30 focus:ring-purple-500'
              }`}
              placeholder={language === 'ar' ? 'اذكر مؤهلاتك وخبراتك ذات الصلة بهذا الدور...' : 'Mention your relevant qualifications and experience for this role...'}
              rows={3}
              dir={language === 'ar' ? 'rtl' : 'ltr'}
            />
            {errors.qualifications && (
              <div className="flex items-center mt-1 text-red-400 text-sm">
                <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
                {errors.qualifications}
              </div>
            )}
          </div>
        </div>
      </div>
    );
  };

  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-6">
            <div className="text-center mb-8">
           
              <h3 className="text-2xl font-bold text-white mb-2">
                {language === 'ar' ? 'المعلومات الأساسية' : 'Basic Information'}
              </h3>
              <p className="text-gray-300">
                {language === 'ar' ? 'أدخل معلوماتك الشخصية' : 'Enter your personal information'}
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  {language === 'ar' ? 'الاسم الأول' : 'First Name'} <span className="text-red-400">*</span>
                </label>
                <input
                  type="text"
                  value={formData.firstName}
                  onChange={(e) => handleInputChange('firstName', e.target.value)}
                  className="w-full px-4 py-3 bg-white/20 border border-white/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white placeholder-gray-400"
                  placeholder={language === 'ar' ? 'أدخل الاسم الأول' : 'Enter first name'}
                  dir={language === 'ar' ? 'rtl' : 'ltr'}
                />
                {errors.firstName && <p className="text-red-400 text-sm mt-1">{errors.firstName}</p>}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  {language === 'ar' ? 'الاسم الأخير' : 'Last Name'} <span className="text-red-400">*</span>
                </label>
                <input
                  type="text"
                  value={formData.lastName}
                  onChange={(e) => handleInputChange('lastName', e.target.value)}
                  className="w-full px-4 py-3 bg-white/20 border border-white/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white placeholder-gray-400"
                  placeholder={language === 'ar' ? 'أدخل الاسم الأخير' : 'Enter last name'}
                  dir={language === 'ar' ? 'rtl' : 'ltr'}
                />
                {errors.lastName && <p className="text-red-400 text-sm mt-1">{errors.lastName}</p>}
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  {language === 'ar' ? 'اسم المستخدم' : 'Username'} <span className="text-red-400">*</span>
                </label>
                <div className="relative">
                  <input
                    type="text"
                    value={formData.username}
                    onChange={(e) => handleInputChange('username', e.target.value)}
                    className={getInputClasses('username')}
                    placeholder={language === 'ar' ? 'أدخل اسم المستخدم' : 'Enter username'}
                    dir={language === 'ar' ? 'rtl' : 'ltr'}
                  />
                  <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                    {renderValidationIcon('username')}
                  </div>
                </div>
                <ValidationMessage field="username" />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  {language === 'ar' ? 'البريد الإلكتروني' : 'Email'} <span className="text-red-400">*</span>
                </label>
                <div className="relative">
                  <input
                    type="email"
                    value={formData.email}
                    onChange={(e) => handleInputChange('email', e.target.value)}
                    className={getInputClasses('email')}
                    placeholder={language === 'ar' ? 'أدخل البريد الإلكتروني' : 'Enter email'}
                    dir={language === 'ar' ? 'rtl' : 'ltr'}
                  />
                  <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                    {renderValidationIcon('email')}
                  </div>
                </div>
                <ValidationMessage field="email" />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  {language === 'ar' ? 'كلمة المرور' : 'Password'} <span className="text-red-400">*</span>
                </label>
                <input
                  type="password"
                  value={formData.password}
                  onChange={(e) => handleInputChange('password', e.target.value)}
                  className="w-full px-4 py-3 bg-white/20 border border-white/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white placeholder-gray-400"
                  placeholder={language === 'ar' ? 'أدخل كلمة المرور' : 'Enter password'}
                  dir={language === 'ar' ? 'rtl' : 'ltr'}
                />
                {errors.password && <p className="text-red-400 text-sm mt-1">{errors.password}</p>}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  {language === 'ar' ? 'تأكيد كلمة المرور' : 'Confirm Password'} <span className="text-red-400">*</span>
                </label>
                <input
                  type="password"
                  value={formData.passwordConfirm}
                  onChange={(e) => handleInputChange('passwordConfirm', e.target.value)}
                  className="w-full px-4 py-3 bg-white/20 border border-white/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white placeholder-gray-400"
                  placeholder={language === 'ar' ? 'أعد إدخال كلمة المرور' : 'Re-enter password'}
                  dir={language === 'ar' ? 'rtl' : 'ltr'}
                />
                {errors.passwordConfirm && <p className="text-red-400 text-sm mt-1">{errors.passwordConfirm}</p>}
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  {language === 'ar' ? 'رقم الهاتف' : 'Phone Number'}
                </label>
                <input
                  type="tel"
                  value={formData.phone}
                  onChange={(e) => handleInputChange('phone', e.target.value)}
                  className="w-full px-4 py-3 bg-white/20 border border-white/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white placeholder-gray-400"
                  placeholder={language === 'ar' ? 'أدخل رقم الهاتف' : 'Enter phone number'}
                  dir={language === 'ar' ? 'rtl' : 'ltr'}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  {language === 'ar' ? 'الموقع' : 'Location'} <span className="text-red-400">*</span>
                </label>
                <input
                  type="text"
                  value={formData.location}
                  onChange={(e) => handleInputChange('location', e.target.value)}
                  className="w-full px-4 py-3 bg-white/20 border border-white/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white placeholder-gray-400"
                  placeholder={language === 'ar' ? 'أدخل موقعك' : 'Enter your location'}
                  dir={language === 'ar' ? 'rtl' : 'ltr'}
                />
                {errors.location && <p className="text-red-400 text-sm mt-1">{errors.location}</p>}
              </div>
            </div>

            {/* Additional Profile Information */}
            <div className="mt-8 p-6 bg-gradient-to-br from-white/10 to-white/5 rounded-xl border border-white/20 backdrop-blur-sm">
              <div className="flex items-center mb-4">
                <div className="w-6 h-6 bg-gradient-to-r from-purple-500 to-blue-500 rounded-lg flex items-center justify-center mr-3">
                  <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                  </svg>
                </div>
                <div>
                  <h4 className="text-lg font-semibold text-white">
                    {language === 'ar' ? 'معلومات إضافية' : 'Additional Information'}
                  </h4>
                  <p className="text-gray-400 text-sm">
                    {language === 'ar' ? 'اختياري - يساعد في تحسين ملفك الشخصي' : 'Optional - helps improve your profile'}
                  </p>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    {language === 'ar' ? 'الشركة' : 'Company'}
                  </label>
                  <input
                    type="text"
                    value={formData.company}
                    onChange={(e) => handleInputChange('company', e.target.value)}
                    className="w-full px-4 py-3 bg-white/20 border border-white/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white placeholder-gray-400"
                    placeholder={language === 'ar' ? 'اسم الشركة' : 'Company name'}
                    dir={language === 'ar' ? 'rtl' : 'ltr'}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    {language === 'ar' ? 'المنصب' : 'Job Title'}
                  </label>
                  <input
                    type="text"
                    value={formData.jobTitle}
                    onChange={(e) => handleInputChange('jobTitle', e.target.value)}
                    className="w-full px-4 py-3 bg-white/20 border border-white/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white placeholder-gray-400"
                    placeholder={language === 'ar' ? 'منصبك الوظيفي' : 'Your job title'}
                    dir={language === 'ar' ? 'rtl' : 'ltr'}
                  />
                </div>
              </div>

              <div className="mt-4">
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  {language === 'ar' ? 'نبذة شخصية' : 'Bio'}
                </label>
                <textarea
                  value={formData.bio}
                  onChange={(e) => handleInputChange('bio', e.target.value)}
                  rows={3}
                  className="w-full px-4 py-3 bg-white/20 border border-white/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white placeholder-gray-400 resize-none"
                  placeholder={language === 'ar' ? 'اكتب نبذة مختصرة عن نفسك...' : 'Write a brief description about yourself...'}
                  dir={language === 'ar' ? 'rtl' : 'ltr'}
                />
              </div>
            </div>
          </div>
        );

      case 2:
        return (
          <div className="space-y-6">
            <div className="text-center mb-10">
              <div className="inline-flex items-center justify-center w-20 h-20 rounded-full bg-gradient-to-r from-purple-600/30 to-blue-600/30 border border-purple-400/50 mb-6 backdrop-blur-sm">
                <Users size={40} className="text-purple-300" />
              </div>
              <h3 className="text-3xl font-bold text-white mb-3 bg-gradient-to-r from-purple-300 to-blue-300 bg-clip-text text-transparent">
                {language === 'ar' ? 'اختر دورك في المجتمع' : 'Choose Your Role in the Community'}
              </h3>
              <p className="text-gray-300 text-lg max-w-2xl mx-auto">
                {language === 'ar'
                  ? 'انضم إلى مجتمع ريادة الأعمال واختر الدور الذي يناسب أهدافك وخبراتك'
                  : 'Join our entrepreneurship community and select the role that matches your goals and expertise'}
              </p>
              <div className="mt-4 flex items-center justify-center space-x-2">
                <div className="w-2 h-2 bg-purple-400 rounded-full animate-pulse"></div>
                <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse delay-75"></div>
                <div className="w-2 h-2 bg-purple-400 rounded-full animate-pulse delay-150"></div>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {roleOptions.map((role) => (
                <div
                  key={role.key}
                  onClick={() => handleInputChange('selectedRole', role.key)}
                  className={`relative p-6 rounded-xl border-2 cursor-pointer transition-all duration-500 transform hover:scale-105 ${
                    formData.selectedRole === role.key
                      ? 'border-purple-400 bg-gradient-to-br from-purple-500/30 to-blue-600/30 shadow-2xl shadow-purple-500/25'
                      : 'border-white/20 bg-gradient-to-br from-white/10 to-white/5 hover:border-purple-300 hover:shadow-xl hover:shadow-purple-500/10'
                  }`}
                >
                  {/* Selection indicator */}
                  {formData.selectedRole === role.key && (
                    <div className="absolute -top-2 -right-2 w-6 h-6 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full flex items-center justify-center">
                      <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    </div>
                  )}

                  {/* Role icon and title */}
                  <div className="flex items-center mb-4">
                    <div className={`w-12 h-12 rounded-lg bg-gradient-to-r ${role.color} flex items-center justify-center text-2xl mr-4 ${isRTL ? 'ml-4 mr-0' : ''}`}>
                      {role.icon}
                    </div>
                    <div>
                      <h4 className="text-xl font-bold text-white mb-1">{role.title}</h4>
                      <p className="text-gray-300 text-sm">{role.description}</p>
                    </div>
                  </div>

                  {/* Community aspect */}
                  <div className="mb-4">
                    <p className="text-purple-200 text-sm font-medium mb-2">
                      🌟 {role.community}
                    </p>
                  </div>

                  {/* Benefits */}
                  <div className="space-y-2">
                    <p className="text-gray-300 text-xs font-semibold uppercase tracking-wide">
                      {language === 'ar' ? 'المزايا:' : 'Benefits:'}
                    </p>
                    <div className="grid grid-cols-2 gap-1">
                      {role.benefits.map((benefit, index) => (
                        <div key={index} className="flex items-center text-xs text-gray-300">
                          <div className="w-1.5 h-1.5 bg-purple-400 rounded-full mr-2 flex-shrink-0"></div>
                          <span className="truncate">{benefit}</span>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Hover effect overlay */}
                  <div className={`absolute inset-0 rounded-xl bg-gradient-to-r ${role.color} opacity-0 transition-opacity duration-300 ${
                    formData.selectedRole !== role.key ? 'hover:opacity-5' : ''
                  }`}></div>
                </div>
              ))}
            </div>
            {errors.selectedRole && <p className="text-red-400 text-sm mt-1">{errors.selectedRole}</p>}

            {/* Role-specific fields */}
            {formData.selectedRole && (
              <div className="mt-8 p-8 bg-gradient-to-br from-white/15 to-white/5 rounded-xl border border-white/30 backdrop-blur-sm shadow-lg">
                <div className="flex items-center mb-6">
                  <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-blue-500 rounded-lg flex items-center justify-center mr-3">
                    <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                  </div>
                  <div>
                    <h4 className="text-xl font-bold text-white">
                      {language === 'ar' ? 'معلومات إضافية' : 'Additional Information'}
                    </h4>
                    <p className="text-gray-300 text-sm">
                      {language === 'ar' ? 'أكمل ملفك الشخصي لتحسين تجربتك' : 'Complete your profile for a better experience'}
                    </p>
                  </div>
                </div>
                {renderRoleSpecificFields()}
                {formData.selectedRole && renderGeneralFields()}
              </div>
            )}
          </div>
        );

      case 3:
        return (
          <div className="space-y-6">
            <div className="text-center mb-8">
              <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-purple-600/20 mb-4">
                <CheckCircle size={32} className="text-purple-400" />
              </div>
              <h3 className="text-2xl font-bold text-white mb-2">
                {language === 'ar' ? 'الشروط والأحكام' : 'Terms & Conditions'}
              </h3>
              <p className="text-gray-300">
                {language === 'ar' ? 'راجع واقبل الشروط لإكمال التسجيل' : 'Review and accept terms to complete registration'}
              </p>
            </div>

            <div className="space-y-4">
              <div className="border border-white/30 rounded-lg p-4">
                <label className={`flex items-start ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <input
                    type="checkbox"
                    checked={formData.agreeToTerms}
                    onChange={(e) => handleInputChange('agreeToTerms', e.target.checked)}
                    className={`mt-1 ${isRTL ? 'ml-3' : 'mr-3'}`}
                  />
                  <span className="text-white">
                    {language === 'ar' ? 'أوافق على الشروط والأحكام' : 'I agree to the Terms and Conditions'}
                  </span>
                </label>
                {errors.agreeToTerms && <p className="text-red-400 text-sm mt-1">{errors.agreeToTerms}</p>}
              </div>

              <div className="border border-white/30 rounded-lg p-4">
                <label className={`flex items-start ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <input
                    type="checkbox"
                    checked={formData.agreeToPrivacy}
                    onChange={(e) => handleInputChange('agreeToPrivacy', e.target.checked)}
                    className={`mt-1 ${isRTL ? 'ml-3' : 'mr-3'}`}
                  />
                  <span className="text-white">
                    {language === 'ar' ? 'أوافق على سياسة الخصوصية' : 'I agree to the Privacy Policy'}
                  </span>
                </label>
                {errors.agreeToPrivacy && <p className="text-red-400 text-sm mt-1">{errors.agreeToPrivacy}</p>}
              </div>
            </div>

            {errors.submit && (
              <div className="bg-red-500/20 border border-red-500 rounded-lg p-4">
                <p className="text-red-400">{errors.submit}</p>
              </div>
            )}

            {(() => {
              console.log('Rendering success section, success state:', success, 'successMessage:', successMessage);
              return success;
            })() && (
              <div className="bg-green-500/20 border border-green-500 rounded-lg p-4">
                <div className="flex items-center mb-2">
                  <CheckCircle className="h-5 w-5 text-green-400 mr-3" />
                  <p className="text-green-400">{successMessage}</p>
                </div>
                <div className="text-center">
                  <p className="text-green-300 text-sm mb-3">
                    {language === 'ar'
                      ? `سيتم توجيهك إلى صفحة تسجيل الدخول خلال ${countdown} ثانية...`
                      : `Redirecting to login page in ${countdown} seconds...`
                    }
                  </p>
                  <button
                    onClick={() => navigate('/login')}
                    className="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg text-sm transition-colors duration-200"
                  >
                    {language === 'ar' ? 'انتقل إلى تسجيل الدخول الآن' : 'Go to Login Now'}
                  </button>
                </div>
              </div>
            )}
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className={`min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-violet-900 flex items-center justify-center p-4 ${isRTL ? 'rtl' : 'ltr'}`} dir={isRTL ? 'rtl' : 'ltr'}>
      <div className="max-w-4xl w-full">
        {/* Header */}
        <div className="text-center mb-8">
          <Link to="/" className="inline-block mb-4">
            <h1 className="text-3xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-purple-400 to-blue-400">
              Yasmeen AI
            </h1>
          </Link>
          <h2 className="text-2xl font-bold text-white mb-2">
            {language === 'ar' ? 'إنشاء حساب جديد' : 'Create Your Account'}
          </h2>
          <p className="text-gray-300">
            {language === 'ar' ? 'انضم إلى منصة ريادة الأعمال الرائدة في المنطقة' : 'Join the leading entrepreneurship platform in the region'}
          </p>
        </div>

        {/* Step Progress Indicator */}
        <div className="bg-black/30 backdrop-blur-sm rounded-lg p-6 border border-white/20 mb-8">
          <StepProgressIndicator />
        </div>

        {/* Form Container */}
        <form onSubmit={(e) => e.preventDefault()} className="bg-black/30 backdrop-blur-sm rounded-lg p-8 shadow-lg border border-white/20 mb-8">
          {renderStep()}
        </form>

        {/* Navigation Buttons */}
        <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
          <button
            onClick={handlePrevious}
            disabled={currentStep === 1}
            className={`px-6 py-3 bg-white/20 border border-white/30 rounded-lg font-medium text-white hover:bg-white/30 transition-all duration-300 flex items-center ${
              currentStep === 1 ? 'opacity-50 cursor-not-allowed' : 'hover:shadow-glow'
            } ${isRTL ? 'flex-row-reverse' : ''}`}
          >
            {isRTL ? <ArrowRight className="w-4 h-4 ml-2" /> : <ArrowLeft className="w-4 h-4 mr-2" />}
            {language === 'ar' ? 'السابق' : 'Previous'}
          </button>

          {currentStep === totalSteps ? (
            <button
              onClick={handleSubmit}
              disabled={loading}
              className="px-6 py-3 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg font-medium text-white hover:shadow-glow transition-all duration-300 flex items-center"
            >
              {loading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  {language === 'ar' ? 'جاري التسجيل...' : 'Creating Account...'}
                </>
              ) : (
                <>
                  <CheckCircle className={`w-4 h-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                  {language === 'ar' ? 'إنشاء الحساب' : 'Create Account'}
                </>
              )}
            </button>
          ) : (
            <button
              onClick={handleNext}
              className="px-6 py-3 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg font-medium text-white hover:shadow-glow transition-all duration-300 flex items-center"
            >
              {language === 'ar' ? 'التالي' : 'Next'}
              {isRTL ? <ArrowLeft className="w-4 h-4 ml-2" /> : <ArrowRight className="w-4 h-4 mr-2" />}
            </button>
          )}
        </div>

        {/* Login Link */}
        <div className="text-center mt-8">
          <p className="text-gray-300">
            {language === 'ar' ? 'لديك حساب بالفعل؟' : 'Already have an account?'}{' '}
            <Link to="/login" className="text-purple-400 hover:text-purple-300 transition-colors font-semibold">
              {language === 'ar' ? 'تسجيل الدخول' : 'Sign In'}
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
};

export default EnhancedRegisterPage;
