/**
 * Super Admin User Approvals Page
 * Glass morphism design matching login page theme
 * Uses the real UserApprovalManager component with proper API integration
 */

import React from 'react';
import { Link } from 'react-router-dom';
import { useLanguage } from '../../hooks/useLanguage';
import UserApprovalManager from '../../components/admin/UserApprovalManager';
import {
  Shield,
  UserCheck,
  ArrowLeft
} from 'lucide-react';

const SuperAdminApprovalsPage: React.FC = () => {
  const { language, isRTL } = useLanguage();

  return (
    <div className={`min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-violet-900 p-4 ${isRTL ? 'rtl' : 'ltr'}`} dir={isRTL ? 'rtl' : 'ltr'}>
      <div className="max-w-7xl mx-auto">
        {/* Header Section - Glass Morphism Style */}
        <div className="text-center mb-8">
          <Link to="/dashboard" className="inline-block mb-4">
            <h1 className="text-3xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-purple-400 to-blue-400">
              Yasmeen AI
            </h1>
          </Link>

          {/* Page Title */}
          <div className="flex items-center justify-center gap-4 mb-4">
            <div className="w-16 h-16 bg-purple-600/20 rounded-full flex items-center justify-center border border-purple-400/30">
              <UserCheck className="w-8 h-8 text-purple-400" />
            </div>
            <div>
              <h2 className="text-2xl font-bold text-white">
                {language === 'ar' ? 'موافقات المستخدمين' : 'User Approvals'}
              </h2>
              <p className="text-gray-300">
                {language === 'ar'
                  ? 'مراجعة وموافقة طلبات تسجيل المستخدمين الجدد'
                  : 'Review and approve new user registration requests'
                }
              </p>
            </div>
          </div>

          {/* Super Admin Badge */}
          <div className="inline-flex items-center gap-2 bg-gradient-to-r from-red-500/20 to-pink-600/20 border border-red-400/30 text-red-300 px-4 py-2 rounded-full backdrop-blur-sm">
            <Shield className="w-4 h-4" />
            <span className="text-sm font-semibold">
              {language === 'ar' ? 'مدير عام' : 'Super Admin'}
            </span>
          </div>
        </div>

        {/* Navigation */}
        <div className="mb-6">
          <Link
            to="/dashboard"
            className="inline-flex items-center gap-2 text-purple-300 hover:text-purple-200 transition-colors"
          >
            <ArrowLeft className="w-4 h-4" />
            {language === 'ar' ? 'العودة إلى لوحة التحكم' : 'Back to Dashboard'}
          </Link>
        </div>

        {/* Main Content - Glass Morphism Container */}
        <div className="bg-black/30 backdrop-blur-sm rounded-lg border border-white/20 overflow-hidden">
          <div className="p-6">
            <UserApprovalManager />
          </div>
        </div>
      </div>
    </div>
  );
};

export default SuperAdminApprovalsPage;
