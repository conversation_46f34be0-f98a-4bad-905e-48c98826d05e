#!/usr/bin/env python3
"""
Check if our latest test user appears in the API response with correct data
"""

import os
import sys
import django
import requests
import json

# Add the backend directory to Python path
backend_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'backend')
sys.path.append(backend_path)

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'yasmeen_ai.settings')
django.setup()

def check_latest_user_api():
    """Check if our latest test user appears in the API with correct motivation/qualifications"""
    
    # Login as superadmin
    login_data = {
        "username": "superadmin",
        "password": "superadmin123"
    }
    
    try:
        login_response = requests.post(
            'http://localhost:8000/api/auth/login/',
            json=login_data,
            headers={'Content-Type': 'application/json'}
        )
        
        if login_response.status_code != 200:
            print(f"Login failed: {login_response.status_code}")
            return
        
        access_token = login_response.json().get('access')
        
        # Get approvals data
        headers = {
            'Authorization': f'Bearer {access_token}',
            'Content-Type': 'application/json'
        }
        
        approvals_response = requests.get(
            'http://localhost:8000/api/users/approvals/',
            headers=headers
        )
        
        if approvals_response.status_code != 200:
            print(f"Approvals API failed: {approvals_response.status_code}")
            return
        
        approvals_data = approvals_response.json()

        print(f"API response type: {type(approvals_data)}")
        if isinstance(approvals_data, dict):
            print(f"Dict keys: {list(approvals_data.keys())}")
            # Check if there's a 'results' key or similar
            if 'results' in approvals_data:
                approvals_list = approvals_data['results']
            elif 'data' in approvals_data:
                approvals_list = approvals_data['data']
            else:
                # Maybe the dict itself contains user data
                print("Dict structure:")
                for key, value in list(approvals_data.items())[:3]:  # Show first 3 items
                    print(f"  {key}: {type(value)}")
                return
        else:
            approvals_list = approvals_data

        # Find users with "debug" or "complete" in username
        test_users = []
        for user in approvals_list:
            if isinstance(user, dict):
                username = user.get('user_username', '')
                if any(keyword in username.lower() for keyword in ['debug', 'complete']):
                    test_users.append(user)
        
        print(f"Found {len(test_users)} test users in API response:")
        
        for user in test_users:
            username = user.get('user_username')
            print(f"\n--- User: {username} ---")
            
            # Check requested_role_info for motivation and qualifications
            role_info = user.get('requested_role_info', {})
            if role_info:
                motivation = role_info.get('motivation', 'NOT FOUND')
                qualifications = role_info.get('qualifications', 'NOT FOUND')
                
                print(f"Motivation: {motivation}")
                print(f"Qualifications: {qualifications}")
                
                # Check if these are real data or placeholder
                if "This is my test motivation" in motivation:
                    print("✅ Motivation contains real test data")
                elif motivation.startswith("Applied for"):
                    print("⚠️  Motivation is placeholder text")
                else:
                    print("❓ Motivation is unknown format")
                
                if "These are my test qualifications" in qualifications:
                    print("✅ Qualifications contains real test data")
                elif qualifications == "Provided during registration process":
                    print("⚠️  Qualifications is placeholder text")
                else:
                    print("❓ Qualifications is unknown format")
            else:
                print("No role info found")
        
        # Also check if our latest user exists in the database but not in API
        from django.contrib.auth.models import User
        latest_debug_user = User.objects.filter(username__startswith='debug_test_').order_by('-id').first()
        
        if latest_debug_user:
            print(f"\nLatest debug user in database: {latest_debug_user.username}")
            api_usernames = [user.get('user_username') for user in approvals_list if isinstance(user, dict)]
            if latest_debug_user.username in api_usernames:
                print("✅ Latest user appears in API response")
            else:
                print("⚠️  Latest user NOT in API response - may need approval or different status")
        
    except Exception as e:
        print(f"Error: {str(e)}")

if __name__ == "__main__":
    check_latest_user_api()
