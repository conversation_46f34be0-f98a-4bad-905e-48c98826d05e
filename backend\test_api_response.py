#!/usr/bin/env python3
"""
Test script to check the API response data for user approvals
"""
import os
import sys
import django
import json
from django.conf import settings

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'yasmeen_ai.settings')
django.setup()

from django.contrib.auth.models import User
from users.models import UserApproval, UserProfile, RoleApplication
from users.serializers.main import UserApprovalSerializer

def test_api_response():
    """Test the API response data structure"""
    print("=== Testing User Approval API Response ===\n")
    
    # Get all user approvals
    approvals = UserApproval.objects.all()
    print(f"Total approvals found: {approvals.count()}")
    
    # Find a mentor user for testing
    mentor_approval = None
    for approval in approvals:
        try:
            role_apps = RoleApplication.objects.filter(user=approval.user)
            for app in role_apps:
                if app.requested_role and app.requested_role.name == 'mentor':
                    mentor_approval = approval
                    break
            if mentor_approval:
                break
        except Exception as e:
            continue
    
    if not mentor_approval:
        print("No mentor approval found. Checking first available approval...")
        mentor_approval = approvals.first()
    
    if not mentor_approval:
        print("No approvals found in database!")
        return
    
    print(f"\nTesting approval for user: {mentor_approval.user.username}")
    print(f"User full name: {mentor_approval.user.first_name} {mentor_approval.user.last_name}")
    
    # Serialize the data
    serializer = UserApprovalSerializer(mentor_approval)
    data = serializer.data
    
    print("\n=== API Response Data Structure ===")
    print(json.dumps(data, indent=2, default=str))
    
    print("\n=== Checking Specific Data Fields ===")
    
    # Check profile summary
    profile_summary = data.get('profile_summary', {})
    print(f"\nProfile Summary:")
    for key, value in profile_summary.items():
        print(f"  {key}: {value}")
    
    # Check role specific data
    role_specific = data.get('role_specific_data', {})
    print(f"\nRole Specific Data:")
    for key, value in role_specific.items():
        print(f"  {key}: {value}")
    
    # Check requested role info
    role_info = data.get('requested_role_info', {})
    print(f"\nRequested Role Info:")
    for key, value in role_info.items():
        print(f"  {key}: {value}")
    
    # Check if user has profile
    print(f"\n=== User Profile Check ===")
    if hasattr(mentor_approval.user, 'profile'):
        profile = mentor_approval.user.profile
        print(f"Profile exists: Yes")
        print(f"Bio: {profile.bio}")
        print(f"Company: {profile.company}")
        print(f"Job Title: {profile.job_title}")
        print(f"Location: {profile.location}")
        print(f"Industry: {profile.industry}")
        print(f"Phone: {profile.phone_number}")
        print(f"Website: {profile.website}")
        print(f"LinkedIn: {profile.linkedin_url}")
    else:
        print("Profile exists: No")
    
    # Check role applications
    print(f"\n=== Role Applications Check ===")
    role_apps = RoleApplication.objects.filter(user=mentor_approval.user)
    print(f"Role applications count: {role_apps.count()}")
    
    for app in role_apps:
        print(f"\nRole Application:")
        print(f"  Requested Role: {app.requested_role}")
        print(f"  Status: {app.status}")
        print(f"  Motivation: {app.motivation}")
        print(f"  Qualifications: {app.qualifications}")
        
        # Check mentor-specific fields
        if hasattr(app, 'expertise_areas'):
            print(f"  Expertise Areas: {app.expertise_areas}")
        if hasattr(app, 'mentoring_experience'):
            print(f"  Mentoring Experience: {app.mentoring_experience}")
        if hasattr(app, 'availability'):
            print(f"  Availability: {app.availability}")
        if hasattr(app, 'preferred_communication'):
            print(f"  Preferred Communication: {app.preferred_communication}")
        if hasattr(app, 'experience'):
            print(f"  Experience: {app.experience}")
        if hasattr(app, 'portfolio_url'):
            print(f"  Portfolio URL: {app.portfolio_url}")

if __name__ == "__main__":
    test_api_response()
