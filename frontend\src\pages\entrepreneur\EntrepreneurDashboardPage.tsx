import React from 'react';
import { UnifiedDashboard } from '../../components/dashboard/unified';

/**
 * ✅ CONSOLIDATED ENTREPRENEUR DASHBOARD PAGE
 *
 * Now uses UnifiedDashboard with entrepreneur role configuration.
 * Provides business development tools through unified architecture.
 *
 * Key Features:
 * - Unified dashboard with entrepreneur privileges
 * - Business plan creation and management
 * - Funding opportunities and applications
 * - Mentorship connections
 * - Progress tracking and analytics
 * - Consistent UI with role-specific sections
 * - Maintainable single-source architecture
 */
const EntrepreneurDashboardPage: React.FC = () => {
  return <UnifiedDashboard role="entrepreneur" />;
};

export default EntrepreneurDashboardPage;
