from django.contrib.auth.models import User
from users.models import UserApproval, RoleApplication
from users.serializers.main import UserApprovalSerializer

# Get the latest user
user = User.objects.order_by('-date_joined').first()
print(f"Latest user: {user.username}")

# Check if UserApproval exists
approval = UserApproval.objects.filter(user=user).first()
print(f"Has UserApproval: {approval is not None}")

if not approval:
    print("Creating UserApproval for testing...")
    approval = UserApproval.objects.create(user=user, status='pending')

print(f"Approval status: {approval.status}")

# Check RoleApplication
role_app = RoleApplication.objects.filter(user=user).first()
print(f"Has RoleApplication: {role_app is not None}")

if role_app:
    print(f"Role: {role_app.requested_role}")
    print(f"Investment range: {getattr(role_app, 'investment_range', 'Not found')}")

# Test the serializer
print("\nTesting UserApprovalSerializer...")
serializer = UserApprovalSerializer(approval)
data = serializer.data

print(f"User username: {data.get('user_username')}")
print(f"Status: {data.get('status')}")

role_specific = data.get('role_specific_data', {})
print(f"Role specific data: {role_specific}")

if not role_specific:
    print("No role specific data - checking why...")
    # Debug the get_role_specific_data method
    role_applications = RoleApplication.objects.filter(user=approval.user)
    print(f"Found {role_applications.count()} role applications for user")
    
    if role_applications.exists():
        app = role_applications.first()
        print(f"First app role: {app.requested_role.name}")
        print(f"App investment range: {getattr(app, 'investment_range', 'Not found')}")
        print(f"App portfolio companies: {getattr(app, 'portfolio_companies', 'Not found')}")
    else:
        print("No role applications found!")
