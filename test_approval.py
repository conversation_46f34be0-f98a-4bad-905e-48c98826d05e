#!/usr/bin/env python3
"""
Test script to verify the complete approval flow including email notifications
"""

import requests
import json

def test_approval_flow():
    """Test the complete approval flow"""
    
    # Step 1: Login as super admin
    login_url = "http://localhost:8000/api/auth/login/"
    login_data = {
        "username": "admin",  # Super admin username
        "password": "admin123"  # Super admin password
    }
    
    try:
        print("Step 1: Logging in as super admin...")
        login_response = requests.post(login_url, json=login_data)
        
        if login_response.status_code != 200:
            print(f"Login failed: {login_response.status_code}")
            print(f"Response: {login_response.text}")
            return False
            
        token = login_response.json().get('access')
        headers = {'Authorization': f'Bearer {token}'}
        print("Login successful!")
        
        # Step 2: Get pending approvals
        print("\nStep 2: Getting pending approvals...")
        approvals_url = "http://localhost:8000/api/users/approvals/"
        approvals_response = requests.get(approvals_url, headers=headers)
        
        if approvals_response.status_code != 200:
            print(f"Failed to get approvals: {approvals_response.status_code}")
            print(f"Response: {approvals_response.text}")
            return False
            
        approvals_data = approvals_response.json()
        approvals = approvals_data.get('results', [])
        print(f"Found {len(approvals)} total approvals")

        # Find pending approvals
        pending_approvals = []
        for a in approvals:
            if isinstance(a, dict) and a.get('status') == 'pending':
                pending_approvals.append(a)

        print(f"Found {len(pending_approvals)} pending approvals")
        
        if not pending_approvals:
            print("No pending approvals found")
            return True
            
        # Step 3: Approve the first pending user
        test_approval = pending_approvals[0]
        approval_id = test_approval['id']
        username = test_approval['user_username']
        
        print(f"\nStep 3: Approving user {username} (ID: {approval_id})...")
        
        approve_url = f"http://localhost:8000/api/users/approvals/{approval_id}/approve/"
        approve_response = requests.post(approve_url, headers=headers)
        
        if approve_response.status_code == 200:
            print(f"User {username} approved successfully!")
            print(f"Response: {approve_response.json()}")
            
            # Step 4: Verify user is now active
            print(f"\nStep 4: Verifying user {username} is now active...")
            user_check_url = f"http://localhost:8000/api/users/users/{test_approval['user']}/"
            user_response = requests.get(user_check_url, headers=headers)
            
            if user_response.status_code == 200:
                user_data = user_response.json()
                is_active = user_data.get('is_active', False)
                print(f"User {username} is_active: {is_active}")
                
                if is_active:
                    print("SUCCESS: User account is now active and can login!")
                    return True
                else:
                    print("ERROR: User account is still not active")
                    return False
            else:
                print(f"Failed to check user status: {user_response.status_code}")
                return False
        else:
            print(f"Failed to approve user: {approve_response.status_code}")
            print(f"Response: {approve_response.text}")
            return False
            
    except Exception as e:
        print(f"Error in approval flow: {e}")
        return False

def test_user_login():
    """Test if the approved user can now login"""
    print("\nStep 5: Testing if approved user can login...")
    
    login_url = "http://localhost:8000/api/auth/login/"
    login_data = {
        "username": "testentrepreneur123",
        "password": "TestPass123!"
    }
    
    try:
        login_response = requests.post(login_url, json=login_data)
        
        if login_response.status_code == 200:
            print("SUCCESS: Approved user can now login!")
            user_data = login_response.json()
            print(f"User role: {user_data.get('user', {}).get('user_role', 'unknown')}")
            return True
        else:
            print(f"Login failed: {login_response.status_code}")
            print(f"Response: {login_response.text}")
            return False
            
    except Exception as e:
        print(f"Error testing user login: {e}")
        return False

if __name__ == "__main__":
    print("Testing Complete Approval Flow...")
    print("=" * 50)
    
    # Test approval flow
    if test_approval_flow():
        # Test user login
        test_user_login()
    
    print("\n" + "=" * 50)
    print("Test completed!")
    print("\nNOTE: Check the Django console for email output!")
    print("The approval email should be printed to the console.")
