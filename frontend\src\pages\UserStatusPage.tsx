/**
 * ✅ USER STATUS PAGE
 * Shows user their current approval status and next steps
 */

import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../hooks/useLanguage';
import { useAppSelector } from '../store/hooks';
import { 
  Clock, 
  CheckCircle, 
  XCircle, 
  Mail, 
  Phone, 
  AlertTriangle,
  User,
  Calendar,
  RefreshCw,
  ArrowRight,
  Home
} from 'lucide-react';
import { Link } from 'react-router-dom';

interface UserStatus {
  status: 'pending' | 'approved' | 'rejected' | 'active';
  submittedAt: string;
  reviewedAt?: string;
  reviewedBy?: string;
  rejectionReason?: string;
  daysPending?: number;
  estimatedReviewTime: string;
}

const UserStatusPage: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const { user } = useAppSelector(state => state.auth);
  const [loading, setLoading] = useState(true);
  const [userStatus, setUserStatus] = useState<UserStatus>({
    status: 'pending',
    submittedAt: new Date().toISOString(),
    estimatedReviewTime: '3-4 business days',
    daysPending: 2
  });

  useEffect(() => {
    // Simulate loading user status
    const timer = setTimeout(() => {
      setLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'approved':
      case 'active':
        return <CheckCircle className="w-8 h-8 text-green-400" />;
      case 'rejected':
        return <XCircle className="w-8 h-8 text-red-400" />;
      case 'pending':
      default:
        return <Clock className="w-8 h-8 text-yellow-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved':
      case 'active':
        return 'bg-green-500/20 border-green-500/30 text-green-300';
      case 'rejected':
        return 'bg-red-500/20 border-red-500/30 text-red-300';
      case 'pending':
      default:
        return 'bg-yellow-500/20 border-yellow-500/30 text-yellow-300';
    }
  };

  const getStatusMessage = (status: string) => {
    switch (status) {
      case 'approved':
      case 'active':
        return {
          title: isRTL ? 'تم قبول حسابك!' : 'Account Approved!',
          message: isRTL ? 'مرحباً بك في منصة ياسمين للذكاء الاصطناعي. يمكنك الآن الوصول إلى جميع الميزات.' : 'Welcome to Yasmeen AI platform. You now have access to all features.'
        };
      case 'rejected':
        return {
          title: isRTL ? 'تم رفض الطلب' : 'Application Rejected',
          message: isRTL ? 'نأسف لإبلاغك أنه تم رفض طلبك. يرجى مراجعة السبب أدناه.' : 'We regret to inform you that your application has been rejected. Please review the reason below.'
        };
      case 'pending':
      default:
        return {
          title: isRTL ? 'طلبك قيد المراجعة' : 'Application Under Review',
          message: isRTL ? 'شكراً لك على التسجيل! طلبك قيد المراجعة من قبل فريقنا.' : 'Thank you for registering! Your application is being reviewed by our team.'
        };
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-indigo-950 to-purple-950 flex items-center justify-center">
        <div className="text-center">
          <RefreshCw className="w-12 h-12 animate-spin text-purple-400 mx-auto mb-4" />
          <p className="text-white text-lg">{isRTL ? 'جاري تحميل حالة الحساب...' : 'Loading account status...'}</p>
        </div>
      </div>
    );
  }

  const statusInfo = getStatusMessage(userStatus.status);

  return (
    <div className="min-h-screen bg-gradient-to-b from-indigo-950 to-purple-950 text-white" dir={isRTL ? 'rtl' : 'ltr'}>
      <div className="max-w-4xl mx-auto p-6">
        {/* Header */}
        <div className={`text-center mb-8 ${isRTL ? 'text-right' : 'text-left'}`}>
          <h1 className="text-3xl font-bold text-white mb-2">
            {isRTL ? 'حالة الحساب' : 'Account Status'}
          </h1>
          <p className="text-purple-200">
            {isRTL ? 'تحقق من حالة طلب التسجيل الخاص بك' : 'Check your registration application status'}
          </p>
        </div>

        {/* Status Card */}
        <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-8 border border-indigo-800/50 mb-6">
          <div className={`flex items-center justify-center mb-6 ${isRTL ? 'flex-row-reverse' : ''}`}>
            {getStatusIcon(userStatus.status)}
          </div>

          <div className="text-center mb-6">
            <h2 className="text-2xl font-bold text-white mb-2">{statusInfo.title}</h2>
            <p className="text-purple-200">{statusInfo.message}</p>
          </div>

          {/* Status Badge */}
          <div className="flex justify-center mb-6">
            <div className={`px-4 py-2 rounded-lg border ${getStatusColor(userStatus.status)}`}>
              <span className="font-semibold">
                {isRTL ? 
                  (userStatus.status === 'pending' ? 'في الانتظار' : 
                   userStatus.status === 'approved' ? 'معتمد' : 
                   userStatus.status === 'active' ? 'نشط' : 'مرفوض') 
                  : userStatus.status.charAt(0).toUpperCase() + userStatus.status.slice(1)}
              </span>
            </div>
          </div>

          {/* User Info */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div className="bg-indigo-800/30 rounded-lg p-4">
              <div className={`flex items-center mb-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
                <User className="w-5 h-5 text-purple-400 mr-2" />
                <span className="text-purple-200">{isRTL ? 'اسم المستخدم' : 'Username'}</span>
              </div>
              <p className="text-white font-semibold">{user?.username}</p>
            </div>

            <div className="bg-indigo-800/30 rounded-lg p-4">
              <div className={`flex items-center mb-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
                <Calendar className="w-5 h-5 text-purple-400 mr-2" />
                <span className="text-purple-200">{isRTL ? 'تاريخ التقديم' : 'Submitted'}</span>
              </div>
              <p className="text-white font-semibold">
                {new Date(userStatus.submittedAt).toLocaleDateString(isRTL ? 'ar-SA' : 'en-US')}
              </p>
            </div>
          </div>

          {/* Status-specific content */}
          {userStatus.status === 'pending' && (
            <div className="bg-yellow-500/10 border border-yellow-500/30 rounded-lg p-4 mb-6">
              <div className={`flex items-center mb-3 ${isRTL ? 'flex-row-reverse' : ''}`}>
                <Clock className="w-5 h-5 text-yellow-400 mr-2" />
                <h3 className="text-yellow-300 font-semibold">
                  {isRTL ? 'معلومات المراجعة' : 'Review Information'}
                </h3>
              </div>
              <div className="space-y-2 text-yellow-200">
                <p>
                  {isRTL ? `أيام في الانتظار: ${userStatus.daysPending}` : `Days pending: ${userStatus.daysPending}`}
                </p>
                <p>
                  {isRTL ? `الوقت المتوقع للمراجعة: ${userStatus.estimatedReviewTime}` : `Estimated review time: ${userStatus.estimatedReviewTime}`}
                </p>
              </div>
            </div>
          )}

          {userStatus.status === 'rejected' && userStatus.rejectionReason && (
            <div className="bg-red-500/10 border border-red-500/30 rounded-lg p-4 mb-6">
              <div className={`flex items-center mb-3 ${isRTL ? 'flex-row-reverse' : ''}`}>
                <AlertTriangle className="w-5 h-5 text-red-400 mr-2" />
                <h3 className="text-red-300 font-semibold">
                  {isRTL ? 'سبب الرفض' : 'Rejection Reason'}
                </h3>
              </div>
              <p className="text-red-200">{userStatus.rejectionReason}</p>
            </div>
          )}

          {/* Contact Information */}
          <div className="bg-indigo-800/30 rounded-lg p-4 mb-6">
            <h3 className="text-white font-semibold mb-3">
              {isRTL ? 'هل تحتاج مساعدة؟' : 'Need Help?'}
            </h3>
            <div className="space-y-2">
              <div className={`flex items-center text-purple-200 ${isRTL ? 'flex-row-reverse' : ''}`}>
                <Mail className="w-4 h-4 mr-2" />
                <span><EMAIL></span>
              </div>
              <div className={`flex items-center text-purple-200 ${isRTL ? 'flex-row-reverse' : ''}`}>
                <Phone className="w-4 h-4 mr-2" />
                <span>+963 987 654 321</span>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className={`flex flex-col sm:flex-row gap-4 justify-center ${isRTL ? 'sm:flex-row-reverse' : ''}`}>
            {userStatus.status === 'approved' || userStatus.status === 'active' ? (
              <Link
                to="/dashboard"
                className={`flex items-center justify-center px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors ${isRTL ? 'flex-row-reverse' : ''}`}
              >
                <Home className="w-5 h-5 mr-2" />
                {isRTL ? 'الذهاب إلى لوحة التحكم' : 'Go to Dashboard'}
                <ArrowRight className="w-5 h-5 ml-2" />
              </Link>
            ) : (
              <Link
                to="/"
                className={`flex items-center justify-center px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors ${isRTL ? 'flex-row-reverse' : ''}`}
              >
                <Home className="w-5 h-5 mr-2" />
                {isRTL ? 'العودة إلى الصفحة الرئيسية' : 'Back to Home'}
              </Link>
            )}

            <button
              onClick={() => window.location.reload()}
              className={`flex items-center justify-center px-6 py-3 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors ${isRTL ? 'flex-row-reverse' : ''}`}
            >
              <RefreshCw className="w-5 h-5 mr-2" />
              {isRTL ? 'تحديث الحالة' : 'Refresh Status'}
            </button>
          </div>
        </div>

        {/* Additional Information */}
        <div className="bg-indigo-900/20 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/30">
          <h3 className="text-white font-semibold mb-4">
            {isRTL ? 'ماذا يحدث بعد ذلك؟' : 'What happens next?'}
          </h3>
          <div className="space-y-3 text-purple-200">
            {userStatus.status === 'pending' && (
              <>
                <p>• {isRTL ? 'سيقوم فريقنا بمراجعة طلبك خلال 3-4 أيام عمل' : 'Our team will review your application within 3-4 business days'}</p>
                <p>• {isRTL ? 'ستتلقى إشعاراً عبر البريد الإلكتروني عند اتخاذ قرار' : 'You will receive an email notification once a decision is made'}</p>
                <p>• {isRTL ? 'يمكنك التحقق من هذه الصفحة في أي وقت لمعرفة آخر التحديثات' : 'You can check this page anytime for the latest updates'}</p>
              </>
            )}
            {userStatus.status === 'approved' && (
              <>
                <p>• {isRTL ? 'يمكنك الآن الوصول إلى جميع ميزات المنصة' : 'You now have access to all platform features'}</p>
                <p>• {isRTL ? 'ابدأ بإنشاء ملفك الشخصي وتحديد أهدافك' : 'Start by creating your profile and setting your goals'}</p>
                <p>• {isRTL ? 'استكشف الموارد والأدوات المتاحة لك' : 'Explore the resources and tools available to you'}</p>
              </>
            )}
            {userStatus.status === 'rejected' && (
              <>
                <p>• {isRTL ? 'يمكنك التقدم مرة أخرى بعد معالجة أسباب الرفض' : 'You can reapply after addressing the rejection reasons'}</p>
                <p>• {isRTL ? 'تواصل معنا إذا كنت بحاجة إلى توضيحات إضافية' : 'Contact us if you need additional clarifications'}</p>
                <p>• {isRTL ? 'نحن هنا لمساعدتك في تحسين طلبك' : 'We are here to help you improve your application'}</p>
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default UserStatusPage;
