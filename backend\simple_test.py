from django.contrib.auth.models import User
from users.models import RoleApplication
from users.serializers.main import UserApprovalSerializer

# Get the latest user
user = User.objects.order_by('-date_joined').first()
print(f"User: {user.username}")

# Get their role application
role_app = RoleApplication.objects.filter(user=user).first()
print(f"Role app exists: {role_app is not None}")

if role_app:
    print(f"Requested role: {role_app.requested_role}")
    print(f"Investment range: {getattr(role_app, 'investment_range', 'Not found')}")
    print(f"Portfolio companies: {getattr(role_app, 'portfolio_companies', 'Not found')}")
    
    # Test the serializer
    serializer = UserApprovalSerializer(role_app)
    data = serializer.data
    
    print("Serializer data:")
    print(f"  User: {data.get('user_username')}")
    print(f"  Role specific data: {data.get('role_specific_data', {})}")
