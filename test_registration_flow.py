#!/usr/bin/env python3
"""
Test script to verify the complete registration flow and data mapping
"""
import requests
import json

def test_registration_with_profile_data():
    """Test registration with complete profile data"""
    
    # Registration data with all profile fields
    registration_data = {
        "first_name": "<PERSON>",
        "last_name": "<PERSON><PERSON>",
        "username": "johndoe_test",
        "email": "<EMAIL>",
        "password": "testpassword123",
        "password_confirm": "testpassword123",
        "phone": "+1234567890",
        "location": "New York, USA",
        "bio": "Experienced entrepreneur with a passion for technology and innovation.",
        "company": "Tech Innovations Inc",
        "job_title": "CEO & Founder",
        "website": "https://techinnovations.com",
        "linkedin_url": "https://linkedin.com/in/johndoe",
        "language": "en",
        "selected_role": "entrepreneur",
        # Entrepreneur specific fields
        "business_name": "AI Solutions Startup",
        "business_stage": "idea",
        "industry": "Technology",
        "funding_needed": "100000-500000",
        "business_description": "AI-powered solutions for small businesses",
        "team_size": "1-5",
        "support_needed": "funding",
        "previous_experience": "5+ years in tech industry",
        "portfolio_url": "https://johndoe.portfolio.com"
    }
    
    print("=== Testing Registration with Complete Profile Data ===")
    print(f"Registration data: {json.dumps(registration_data, indent=2)}")
    
    # Send registration request
    try:
        response = requests.post(
            "http://localhost:8000/api/auth/register-enhanced/",
            json=registration_data,
            headers={"Content-Type": "application/json"}
        )
        
        print(f"\nResponse Status: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        
        if response.status_code == 201:
            response_data = response.json()
            print(f"Registration successful!")
            print(f"Response data: {json.dumps(response_data, indent=2)}")
            
            # Get the user ID for further testing
            user_id = response_data.get('user', {}).get('id')
            if user_id:
                print(f"\nUser created with ID: {user_id}")
                return user_id
        else:
            print(f"Registration failed!")
            try:
                error_data = response.json()
                print(f"Error data: {json.dumps(error_data, indent=2)}")
            except:
                print(f"Error text: {response.text}")
                
    except Exception as e:
        print(f"Request failed: {e}")
        
    return None

def test_approval_data_retrieval(user_id=None):
    """Test retrieving approval data to see what's available"""
    
    print("\n=== Testing Approval Data Retrieval ===")
    
    try:
        # Get approvals data (this would normally require admin authentication)
        response = requests.get(
            "http://localhost:8000/api/users/approvals/",
            headers={"Content-Type": "application/json"}
        )
        
        print(f"Approvals Response Status: {response.status_code}")
        
        if response.status_code == 200:
            approvals_data = response.json()
            print(f"Approvals data retrieved successfully!")
            print(f"Total approvals: {approvals_data.get('count', 0)}")
            
            # Find our test user if user_id provided
            if user_id and 'results' in approvals_data:
                for approval in approvals_data['results']:
                    if approval.get('user') == user_id:
                        print(f"\nFound our test user approval:")
                        print(f"Profile Summary: {json.dumps(approval.get('profile_summary', {}), indent=2)}")
                        print(f"Role Specific Data: {json.dumps(approval.get('role_specific_data', {}), indent=2)}")
                        break
                else:
                    print(f"\nTest user with ID {user_id} not found in approvals")
            
            # Show first approval as example
            if approvals_data.get('results'):
                first_approval = approvals_data['results'][0]
                print(f"\nExample approval data structure:")
                print(f"Profile Summary: {json.dumps(first_approval.get('profile_summary', {}), indent=2)}")
                print(f"Role Specific Data: {json.dumps(first_approval.get('role_specific_data', {}), indent=2)}")
                
        else:
            print(f"Failed to retrieve approvals data")
            try:
                error_data = response.json()
                print(f"Error: {json.dumps(error_data, indent=2)}")
            except:
                print(f"Error text: {response.text}")
                
    except Exception as e:
        print(f"Approvals request failed: {e}")

if __name__ == "__main__":
    # Test registration
    user_id = test_registration_with_profile_data()
    
    # Test approval data retrieval
    test_approval_data_retrieval(user_id)
    
    print("\n=== Test Summary ===")
    print("1. Registration test completed")
    print("2. Approval data retrieval test completed")
    print("3. Check the super admin interface to see if the new profile data is displayed")
