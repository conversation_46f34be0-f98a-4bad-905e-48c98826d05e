/**
 * 🎯 DEMO E2E TEST
 * Demonstrates our comprehensive testing capabilities on the actual application
 */

import { test, expect } from '@playwright/test';

test.describe('🎯 Demo E2E Tests - Cleaned Codebase Validation', () => {
  
  test('🚀 Should load application successfully', async ({ page }) => {
    console.log('🧪 Testing application load...');
    
    await page.goto('/');
    
    // Check if the page loads
    await expect(page.locator('body')).toBeVisible();
    
    // Check if we have some content
    const pageContent = await page.textContent('body');
    expect(pageContent).toBeTruthy();
    
    console.log('✅ Application loaded successfully!');
  });

  test('🔍 Should have proper page structure', async ({ page }) => {
    console.log('🧪 Testing page structure...');
    
    await page.goto('/');
    
    // Check for basic HTML structure
    await expect(page.locator('html')).toBeVisible();
    await expect(page.locator('head')).toBeVisible();
    await expect(page.locator('body')).toBeVisible();
    
    // Check if React app is mounted
    const reactRoot = page.locator('#root');
    if (await reactRoot.isVisible()) {
      console.log('✅ React app is properly mounted');
    }
    
    console.log('✅ Page structure is correct!');
  });

  test('📱 Should be responsive on mobile', async ({ page }) => {
    console.log('🧪 Testing mobile responsiveness...');
    
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    
    await page.goto('/');
    
    // Check if page is visible on mobile
    await expect(page.locator('body')).toBeVisible();
    
    // Check if content fits in mobile viewport
    const bodyBox = await page.locator('body').boundingBox();
    expect(bodyBox?.width).toBeLessThanOrEqual(375);
    
    console.log('✅ Mobile responsiveness working!');
  });

  test('🌐 Should handle navigation', async ({ page }) => {
    console.log('🧪 Testing navigation...');
    
    await page.goto('/');
    
    // Try to navigate to different routes
    const routes = ['/login', '/register', '/about'];
    
    for (const route of routes) {
      try {
        await page.goto(route);
        await expect(page.locator('body')).toBeVisible();
        console.log(`✅ Route ${route} loads correctly`);
      } catch (error) {
        console.log(`⚠️ Route ${route} may not exist or need authentication`);
      }
    }
    
    console.log('✅ Navigation testing completed!');
  });

  test('⚡ Should meet performance benchmarks', async ({ page }) => {
    console.log('🧪 Testing performance...');
    
    const startTime = Date.now();
    
    await page.goto('/');
    await expect(page.locator('body')).toBeVisible();
    
    const loadTime = Date.now() - startTime;
    
    // Check if page loads within 5 seconds (reasonable for development)
    expect(loadTime).toBeLessThan(5000);
    
    console.log(`✅ Page loaded in ${loadTime}ms (under 5s benchmark)`);
  });

  test('🔧 Should handle errors gracefully', async ({ page }) => {
    console.log('🧪 Testing error handling...');
    
    // Listen for console errors
    const consoleErrors: string[] = [];
    page.on('console', msg => {
      if (msg.type() === 'error') {
        consoleErrors.push(msg.text());
      }
    });
    
    await page.goto('/');
    await expect(page.locator('body')).toBeVisible();
    
    // Try to navigate to a non-existent route
    await page.goto('/non-existent-route');
    
    // Should still have a body (error page or redirect)
    await expect(page.locator('body')).toBeVisible();
    
    console.log(`Console errors detected: ${consoleErrors.length}`);
    console.log('✅ Error handling test completed!');
  });

  test('🎨 Should have consistent styling', async ({ page }) => {
    console.log('🧪 Testing styling consistency...');
    
    await page.goto('/');
    
    // Check if CSS is loaded
    const bodyStyles = await page.locator('body').evaluate(el => {
      const styles = window.getComputedStyle(el);
      return {
        fontFamily: styles.fontFamily,
        backgroundColor: styles.backgroundColor,
        margin: styles.margin
      };
    });
    
    expect(bodyStyles.fontFamily).toBeTruthy();
    
    console.log('✅ Styling consistency verified!');
  });

  test('🔄 Should handle page refresh', async ({ page }) => {
    console.log('🧪 Testing page refresh...');
    
    await page.goto('/');
    await expect(page.locator('body')).toBeVisible();
    
    // Refresh the page
    await page.reload();
    await expect(page.locator('body')).toBeVisible();
    
    console.log('✅ Page refresh handled correctly!');
  });

});
