#!/usr/bin/env python3
"""
Check what data is actually stored for our test user
"""

import os
import sys
import django

# Add the backend directory to Python path
backend_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'backend')
sys.path.append(backend_path)

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'yasmeen_ai.settings')
django.setup()

from django.contrib.auth.models import User
from users.models import UserProfile, RoleApplication, UserRoleAssignment

def check_latest_user():
    """Check the latest registered user's data"""
    
    # Get the latest user
    user = User.objects.filter(username__startswith='complete_test_').order_by('-id').first()
    
    if not user:
        print("No test user found")
        return
    
    print(f"Checking user: {user.username} (ID: {user.id})")
    
    # Get profile
    profile = UserProfile.objects.get(user=user)
    print(f"Profile ID: {profile.id}")
    
    # Check role_additional_info
    print(f"\nRole Additional Info:")
    role_info = profile.role_additional_info
    if role_info:
        for key, value in role_info.items():
            print(f"  {key}: {value}")
    else:
        print("  No role additional info")
    
    # Check if motivation and qualifications are in role_additional_info
    motivation = role_info.get('motivation', 'NOT FOUND')
    qualifications = role_info.get('qualifications', 'NOT FOUND')
    
    print(f"\nMotivation: {motivation}")
    print(f"Qualifications: {qualifications}")
    
    # Check role applications
    role_apps = RoleApplication.objects.filter(user=user)
    print(f"\nRole Applications: {role_apps.count()}")
    
    for app in role_apps:
        print(f"  Application ID: {app.id}")
        print(f"  Role: {app.requested_role.name}")
        print(f"  Status: {app.status}")
        print(f"  Motivation: {app.motivation}")
        print(f"  Qualifications: {app.qualifications}")
        print(f"  Experience: {app.experience}")
        print(f"  Portfolio URL: {app.portfolio_url}")
        print("  ---")

if __name__ == "__main__":
    check_latest_user()
