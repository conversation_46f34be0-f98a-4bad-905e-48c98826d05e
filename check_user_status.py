#!/usr/bin/env python3
"""
Check the status of our latest test user to see why it's not in the approvals API
"""

import os
import sys
import django

# Add the backend directory to Python path
backend_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'backend')
sys.path.append(backend_path)

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'yasmeen_ai.settings')
django.setup()

from django.contrib.auth.models import User
from users.models import UserProfile, RoleApplication, UserRoleAssignment

def check_user_status():
    """Check why our latest user isn't in the approvals API"""
    
    # Get the latest debug user
    latest_user = User.objects.filter(username__startswith='debug_test_').order_by('-id').first()
    
    if not latest_user:
        print("No debug test user found")
        return
    
    print(f"Checking user: {latest_user.username} (ID: {latest_user.id})")
    
    # Get profile
    try:
        profile = UserProfile.objects.get(user=latest_user)
        print(f"Profile ID: {profile.id}")
        print(f"Profile is_active: {profile.is_active}")
        print(f"User is_active: {latest_user.is_active}")
        print(f"User is_staff: {latest_user.is_staff}")
    except UserProfile.DoesNotExist:
        print("No profile found")
        return
    
    # Check role applications
    role_apps = RoleApplication.objects.filter(user=latest_user)
    print(f"\nRole Applications: {role_apps.count()}")
    
    for app in role_apps:
        print(f"  Application ID: {app.id}")
        print(f"  Role: {app.requested_role.name}")
        print(f"  Status: {app.status}")
        print(f"  Created: {app.created_at}")
        print(f"  Motivation: {app.motivation}")
        print(f"  Qualifications: {app.qualifications}")
    
    # Check role assignments
    role_assignments = UserRoleAssignment.objects.filter(user_profile=profile)
    print(f"\nRole Assignments: {role_assignments.count()}")
    
    for assignment in role_assignments:
        print(f"  Role: {assignment.role.name}")
        print(f"  Is Active: {assignment.is_active}")
        print(f"  Is Approved: {assignment.is_approved}")
        print(f"  Assigned At: {assignment.assigned_at}")
    
    # Check what the approvals API should return
    print(f"\n=== Checking Approvals API Logic ===")
    
    # The approvals API typically returns users with pending role applications
    pending_apps = RoleApplication.objects.filter(user=latest_user, status='pending')
    print(f"Pending applications: {pending_apps.count()}")
    
    # Or users who need approval
    print(f"User requires approval: {not latest_user.is_active}")
    
    # Check if user should appear in approvals based on the API logic
    if role_apps.exists():
        app = role_apps.first()
        if app.status == 'pending':
            print("✅ User should appear in approvals (has pending application)")
        else:
            print(f"⚠️  User won't appear in approvals (application status: {app.status})")
    else:
        print("⚠️  User won't appear in approvals (no role applications)")

if __name__ == "__main__":
    check_user_status()
