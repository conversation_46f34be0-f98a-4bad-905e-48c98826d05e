#!/usr/bin/env python
"""
Create a super admin user for testing
"""
import os
import sys
import django

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'yasmeen_ai.settings')
django.setup()

from django.contrib.auth.models import User
from users.models.main import UserProfile, UserRole, UserRoleAssignment

def create_super_admin():
    """Create a super admin user"""
    try:
        # Check if super admin already exists
        if User.objects.filter(username='superadmin').exists():
            user = User.objects.get(username='superadmin')
            user.set_password('superadmin123')
            user.save()
            print("[SUCCESS] Updated password for existing super admin user")
        else:
            # Create new super admin user
            user = User.objects.create_user(
                username='superadmin',
                email='<EMAIL>',
                password='superadmin123',
                is_staff=True,
                is_superuser=True
            )
            print("[SUCCESS] Created new super admin user")

        # Create or update user profile
        profile, created = UserProfile.objects.get_or_create(
            user=user,
            defaults={
                'bio': 'Super Administrator - Full System Access',
                'location': 'Damascus, Syria',
                'language': 'ar'
            }
        )

        if created:
            print("[SUCCESS] Created super admin profile")
        else:
            print("[SUCCESS] Super admin profile already exists")

        # Get super admin role
        try:
            super_admin_role = UserRole.objects.get(name='super_admin')
            print("[SUCCESS] Found super admin role")
        except UserRole.DoesNotExist:
            print("[ERROR] Super admin role not found. Please run create_roles.py first.")
            return False

        # Assign super admin role to user
        role_assignment, assignment_created = UserRoleAssignment.objects.get_or_create(
            user_profile=profile,
            role=super_admin_role,
            defaults={
                'assigned_by': user,
                'is_active': True,
                'is_approved': True
            }
        )

        if assignment_created:
            print("[SUCCESS] Assigned super admin role to user")
        else:
            print("[SUCCESS] User already has super admin role")

        print("\n" + "="*50)
        print("SUPER ADMIN CREATED SUCCESSFULLY!")
        print("="*50)
        print(f"Username: superadmin")
        print(f"Password: superadmin123")
        print(f"Email: <EMAIL>")
        print(f"Role: {super_admin_role.display_name}")
        print(f"Access: Full system control and management")
        print("="*50)

        return True

    except Exception as e:
        print(f"[ERROR] Error creating super admin: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    print("Creating super admin user...")
    if create_super_admin():
        print("\nSuper admin user created successfully!")
        print("You can now login with:")
        print("  Username: superadmin")
        print("  Password: superadmin123")
    else:
        print("\nFailed to create super admin user.")
