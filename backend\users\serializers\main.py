from rest_framework import serializers
from django.contrib.auth.models import User
from django.db.models import Count
from ..models import UserProfile, UserRole, UserRoleAssignment, RoleApplication, UserApproval


class UserRoleSerializer(serializers.ModelSerializer):
    class Meta:
        model = UserRole
        fields = ['id', 'name', 'display_name', 'description', 'permission_level', 'is_active', 'requires_approval']


class UserRoleAssignmentSerializer(serializers.ModelSerializer):
    role = UserRoleSerializer(read_only=True)

    class Meta:
        model = UserRoleAssignment
        fields = ['id', 'role', 'assigned_at', 'expires_at', 'is_active', 'notes']


class RoleApplicationSerializer(serializers.ModelSerializer):
    requested_role = UserRoleSerializer(read_only=True)
    user = serializers.StringRelatedField(read_only=True)

    class Meta:
        model = RoleApplication
        fields = ['id', 'user', 'requested_role', 'motivation', 'qualifications',
                 'experience', 'portfolio_url', 'status', 'created_at', 'reviewed_at', 'admin_notes',
                 # Entrepreneur fields
                 'company_name', 'project_stage', 'industry', 'project_description',
                 'funding_needed', 'team_size', 'support_needed', 'previous_experience',
                 # Mentor fields
                 'expertise_areas', 'mentoring_experience', 'availability', 'preferred_communication',
                 # Investor fields
                 'investment_range', 'investment_focus', 'investment_stage', 'portfolio_companies', 'due_diligence_requirements',
                 # Community Member fields
                 'interests', 'goals']
        read_only_fields = ['user', 'status', 'reviewed_at', 'admin_notes']


class UserProfileSerializer(serializers.ModelSerializer):
    completion_percentage = serializers.SerializerMethodField()
    active_roles = serializers.SerializerMethodField()
    highest_permission_level = serializers.SerializerMethodField()

    class Meta:
        model = UserProfile
        fields = ['id', 'bio', 'location', 'birth_date', 'phone_number', 'profile_image',
                  'website', 'linkedin_url', 'twitter_url', 'github_url', 'company',
                  'job_title', 'industry', 'experience_years', 'is_active',
                  'email_notifications', 'marketing_emails', 'profile_visibility',
                  'language', 'created_at', 'updated_at', 'last_activity',
                  'completion_percentage', 'active_roles', 'highest_permission_level']
        read_only_fields = ['created_at', 'updated_at', 'last_activity']

    def get_completion_percentage(self, obj):
        """Calculate profile completion percentage"""
        total_fields = 10
        completed_fields = 0
        
        if obj.bio: completed_fields += 1
        if obj.location: completed_fields += 1
        if obj.birth_date: completed_fields += 1
        if obj.phone_number: completed_fields += 1
        if obj.profile_image: completed_fields += 1
        if obj.website: completed_fields += 1
        if obj.company: completed_fields += 1
        if obj.job_title: completed_fields += 1
        if obj.industry: completed_fields += 1
        if obj.experience_years: completed_fields += 1
        
        return int((completed_fields / total_fields) * 100)

    def get_active_roles(self, obj):
        """Get user's active roles"""
        try:
            assignments = UserRoleAssignment.objects.filter(user_profile=obj, is_active=True)
            roles_data = UserRoleSerializer([assignment.role for assignment in assignments], many=True).data

            # If no role assignments, check requested_role_name or default to user
            if not roles_data:
                # Use requested_role_name if available, otherwise default to user
                role_name = getattr(obj, 'requested_role_name', 'user') or 'user'

                # Map role names to display names and permission levels
                role_mapping = {
                    'user': {'display_name': 'Regular User', 'permission_level': 'read'},
                    'entrepreneur': {'display_name': 'Entrepreneur', 'permission_level': 'write'},
                    'mentor': {'display_name': 'Mentor', 'permission_level': 'write'},
                    'investor': {'display_name': 'Investor', 'permission_level': 'write'},
                    'moderator': {'display_name': 'Moderator', 'permission_level': 'moderate'},
                    'admin': {'display_name': 'Administrator', 'permission_level': 'admin'},
                    'super_admin': {'display_name': 'Super Administrator', 'permission_level': 'super_admin'}
                }

                role_info = role_mapping.get(role_name, role_mapping['user'])
                roles_data = [{
                    'name': role_name,
                    'display_name': role_info['display_name'],
                    'permission_level': role_info['permission_level']
                }]

            return roles_data
        except Exception as e:
            # Fallback to default user role if there are any issues
            return [{
                'name': 'user',
                'display_name': 'Regular User',
                'permission_level': 'read'
            }]

    def get_highest_permission_level(self, obj):
        """Get user's highest permission level"""
        assignments = UserRoleAssignment.objects.filter(user_profile=obj, is_active=True)

        permission_hierarchy = {
            'read': 1,
            'write': 2,
            'moderate': 3,
            'admin': 4,
            'super_admin': 5
        }

        # If no assignments, use requested_role_name to determine permission level
        if not assignments:
            role_name = getattr(obj, 'requested_role_name', 'user') or 'user'
            role_permission_mapping = {
                'user': 'read',
                'entrepreneur': 'write',
                'mentor': 'write',
                'investor': 'write',
                'moderator': 'moderate',
                'admin': 'admin',
                'super_admin': 'super_admin'
            }
            return role_permission_mapping.get(role_name, 'read')

        highest_level = 'read'
        highest_value = 1

        for assignment in assignments:
            level_value = permission_hierarchy.get(assignment.role.permission_level, 1)
            if level_value > highest_value:
                highest_value = level_value
                highest_level = assignment.role.permission_level

        return highest_level


class UserSerializer(serializers.ModelSerializer):
    profile = UserProfileSerializer(read_only=True)
    full_name = serializers.SerializerMethodField()
    is_staff_member = serializers.SerializerMethodField()
    role_assignments = serializers.SerializerMethodField()
    user_role = serializers.SerializerMethodField()

    class Meta:
        model = User
        fields = ['id', 'username', 'email', 'first_name', 'last_name', 'full_name',
                  'is_active', 'is_staff', 'is_superuser', 'date_joined', 'last_login',
                  'profile', 'is_staff_member', 'role_assignments', 'user_role']
        read_only_fields = ['id', 'date_joined', 'last_login', 'is_staff_member']

    def get_full_name(self, obj):
        return f"{obj.first_name} {obj.last_name}".strip() or obj.username

    def get_is_staff_member(self, obj):
        """Check if user has any staff-level roles"""
        # Temporarily simplified to avoid database schema issues
        return obj.is_staff or obj.is_superuser

    def get_role_assignments(self, obj):
        """Get user's role assignments"""
        # Temporarily return empty list to avoid database schema issues
        return []

    def get_user_role(self, obj):
        """Get user's primary role for frontend role detection"""
        # Check for highest roles first (Django fields)
        if obj.is_superuser:
            return 'super_admin'
        elif obj.is_staff:
            return 'admin'

        # Check profile-based roles
        try:
            profile = obj.profile

            # Check active role assignments
            assignments = UserRoleAssignment.objects.filter(user_profile=profile, is_active=True)
            if assignments.exists():
                # Get the highest priority role
                role_priority = {
                    'super_admin': 6,
                    'admin': 5,
                    'moderator': 4,
                    'entrepreneur': 3,
                    'mentor': 3,
                    'investor': 2,
                    'user': 1
                }

                highest_role = 'user'
                highest_priority = 0

                for assignment in assignments:
                    role_name = assignment.role.name
                    priority = role_priority.get(role_name, 0)
                    if priority > highest_priority:
                        highest_priority = priority
                        highest_role = role_name

                return highest_role

            # Check if user has a requested role during registration
            if hasattr(profile, 'requested_role_name') and profile.requested_role_name:
                return profile.requested_role_name

        except UserProfile.DoesNotExist:
            pass

        # Default to regular user
        return 'user'





class EnhancedRegistrationSerializer(serializers.ModelSerializer):
    """Enhanced registration with profile information and role assignment"""
    # Basic user fields
    password = serializers.CharField(write_only=True, min_length=8)
    password_confirm = serializers.CharField(write_only=True)

    # Profile fields
    bio = serializers.CharField(max_length=500, required=False, allow_blank=True)
    location = serializers.CharField(max_length=100, required=False, allow_blank=True)
    company = serializers.CharField(max_length=200, required=False, allow_blank=True)
    job_title = serializers.CharField(max_length=100, required=False, allow_blank=True)
    industry = serializers.CharField(max_length=100, required=False, allow_blank=True)
    website = serializers.URLField(required=False, allow_blank=True)
    linkedin_url = serializers.URLField(required=False, allow_blank=True)
    language = serializers.ChoiceField(choices=[('en', 'English'), ('ar', 'Arabic')], default='en')

    # Role assignment fields
    selected_role = serializers.CharField(max_length=50, required=False, allow_blank=True)
    role_additional_info = serializers.JSONField(required=False, default=dict)

    # Additional contact fields
    phone = serializers.CharField(max_length=20, required=False, allow_blank=True)

    # Role-specific fields - Entrepreneur
    business_name = serializers.CharField(max_length=200, required=False, allow_blank=True)
    business_stage = serializers.CharField(max_length=50, required=False, allow_blank=True)
    funding_needed = serializers.CharField(max_length=50, required=False, allow_blank=True)
    business_description = serializers.CharField(max_length=1000, required=False, allow_blank=True)
    team_size = serializers.CharField(max_length=50, required=False, allow_blank=True)
    support_needed = serializers.CharField(max_length=1000, required=False, allow_blank=True)
    previous_experience = serializers.CharField(max_length=1000, required=False, allow_blank=True)

    # Role-specific fields - Mentor
    expertise = serializers.CharField(max_length=200, required=False, allow_blank=True)
    experience = serializers.CharField(max_length=50, required=False, allow_blank=True)
    mentorship_areas = serializers.CharField(max_length=1000, required=False, allow_blank=True)
    availability = serializers.CharField(max_length=50, required=False, allow_blank=True)
    preferred_communication = serializers.CharField(max_length=50, required=False, allow_blank=True)

    # Role-specific fields - Investor
    investment_range = serializers.CharField(max_length=50, required=False, allow_blank=True)
    investment_stage = serializers.CharField(max_length=50, required=False, allow_blank=True)
    preferred_industries = serializers.CharField(max_length=1000, required=False, allow_blank=True)
    investment_criteria = serializers.CharField(max_length=1000, required=False, allow_blank=True)
    portfolio_companies = serializers.CharField(max_length=1000, required=False, allow_blank=True)

    # Role-specific fields - Community Member/User
    interests = serializers.CharField(max_length=1000, required=False, allow_blank=True)
    goals = serializers.CharField(max_length=1000, required=False, allow_blank=True)

    # General fields (for all roles)
    portfolio_url = serializers.URLField(required=False, allow_blank=True)
    motivation = serializers.CharField(max_length=1000, required=False, allow_blank=True)
    qualifications = serializers.CharField(max_length=1000, required=False, allow_blank=True)

    class Meta:
        model = User
        fields = ['username', 'email', 'first_name', 'last_name', 'password', 'password_confirm'] + [
            'bio', 'location', 'company', 'job_title', 'industry',
            'website', 'linkedin_url', 'language', 'selected_role',
            'role_additional_info', 'phone',
            # Entrepreneur fields
            'business_name', 'business_stage', 'funding_needed', 'business_description',
            'team_size', 'support_needed', 'previous_experience',
            # Mentor fields
            'expertise', 'experience', 'mentorship_areas', 'availability', 'preferred_communication',
            # Investor fields
            'investment_range', 'investment_stage', 'preferred_industries', 'investment_criteria',
            'portfolio_companies',
            # Community Member fields
            'interests', 'goals',
            # General fields
            'portfolio_url', 'motivation', 'qualifications'
        ]

    def validate(self, data):
        if data['password'] != data['password_confirm']:
            raise serializers.ValidationError("Passwords don't match")
        return data

    def create(self, validated_data):
        # Extract profile data
        profile_data = {
            'bio': validated_data.pop('bio', ''),
            'location': validated_data.pop('location', ''),
            'company': validated_data.pop('company', ''),
            'job_title': validated_data.pop('job_title', ''),
            'industry': validated_data.pop('industry', ''),
            'website': validated_data.pop('website', ''),
            'linkedin_url': validated_data.pop('linkedin_url', ''),
            'language': validated_data.pop('language', 'en'),
            'phone_number': validated_data.pop('phone', ''),  # Map phone to phone_number
        }

        # Extract role assignment data
        selected_role = validated_data.pop('selected_role', '')
        role_additional_info = validated_data.pop('role_additional_info', {})

        # Extract motivation and qualifications from top-level fields
        motivation = validated_data.pop('motivation', '')
        qualifications = validated_data.pop('qualifications', '')

        # Add motivation and qualifications to role_additional_info
        if motivation:
            role_additional_info['motivation'] = motivation
        if qualifications:
            role_additional_info['qualifications'] = qualifications

        # Extract role-specific fields and map them to profile fields where applicable
        role_specific_data = {
            # Entrepreneur fields
            'business_name': validated_data.pop('business_name', ''),
            'business_stage': validated_data.pop('business_stage', ''),
            'funding_needed': validated_data.pop('funding_needed', ''),
            'business_description': validated_data.pop('business_description', ''),
            'team_size': validated_data.pop('team_size', ''),
            'support_needed': validated_data.pop('support_needed', ''),
            'previous_experience': validated_data.pop('previous_experience', ''),
            # Mentor fields
            'expertise': validated_data.pop('expertise', ''),
            'experience': validated_data.pop('experience', ''),  # Keep as experience for consistency
            'mentorship_areas': validated_data.pop('mentorship_areas', ''),
            'availability': validated_data.pop('availability', ''),
            'preferred_communication': validated_data.pop('preferred_communication', ''),
            # Investor fields
            'investment_range': validated_data.pop('investment_range', ''),
            'investment_stage': validated_data.pop('investment_stage', ''),
            'preferred_industries': validated_data.pop('preferred_industries', ''),
            'investment_criteria': validated_data.pop('investment_criteria', ''),
            'portfolio_companies': validated_data.pop('portfolio_companies', ''),
            # Community Member fields
            'interests': validated_data.pop('interests', ''),
            'goals': validated_data.pop('goals', ''),
            # General fields
            'portfolio_url': validated_data.pop('portfolio_url', ''),
        }

        # Create user
        validated_data.pop('password_confirm')
        password = validated_data.pop('password')
        user = User.objects.create_user(**validated_data)
        user.set_password(password)
        # Set user as inactive by default - requires admin approval
        user.is_active = False
        user.save()

        # Ensure profile exists (get or create to handle signal timing issues)
        from users.models import UserProfile
        profile, created = UserProfile.objects.get_or_create(
            user=user,
            defaults={'language': 'en', 'is_active': True}
        )

        # Update profile with additional data and save
        for key, value in profile_data.items():
            setattr(profile, key, value)

        # Store the requested role name in the profile
        if selected_role:
            profile.requested_role_name = selected_role

        # Save role-specific data directly to profile fields
        for key, value in role_specific_data.items():
            if hasattr(profile, key) and value:  # Only set if field exists and value is not empty
                setattr(profile, key, value)

        # Also store role-specific data in the profile's role_additional_info field for backward compatibility
        # Merge with existing role_additional_info if any
        existing_role_info = profile.role_additional_info or {}
        existing_role_info.update(role_specific_data)
        profile.role_additional_info = existing_role_info

        profile.save()

        # Handle role assignment for all users
        if selected_role:
            # Import here to avoid circular imports
            from ..models import UserRole, RoleApplication, UserRoleAssignment
            import logging
            logger = logging.getLogger(__name__)

            try:
                # Get or create the role
                user_role, created = UserRole.objects.get_or_create(
                    name=selected_role,
                    defaults={
                        'display_name': selected_role.replace('_', ' ').title(),
                        'description': f'{selected_role.replace("_", " ").title()} role',
                        'permission_level': 'read' if selected_role == 'user' else 'write',
                        'requires_approval': selected_role != 'user'
                    }
                )

                if selected_role == 'user':
                    # For regular users, create role assignment immediately
                    UserRoleAssignment.objects.get_or_create(
                        user_profile=profile,
                        role=user_role,
                        defaults={
                            'is_active': True,
                            'is_approved': True,
                            'assigned_by': None,  # Auto-assigned during registration
                            'notes': 'Auto-assigned during registration'
                        }
                    )
                    logger.info(f"Created role assignment for user {user.username} with role {selected_role}")
                else:
                    # For special roles, create role application for approval
                    # Ensure role_additional_info is a dictionary
                    if not isinstance(role_additional_info, dict):
                        role_additional_info = {}

                    # Create role application with comprehensive role-specific data
                    role_application_data = {
                        'user': user,
                        'requested_role': user_role,
                        'motivation': role_additional_info.get('motivation', f'Applied for {selected_role} role during registration'),
                        'qualifications': role_additional_info.get('qualifications', 'Provided during registration process'),
                        'experience': role_specific_data.get('experience', ''),
                        'portfolio_url': role_specific_data.get('portfolio_url', ''),
                        'status': 'pending'
                    }

                    # Add role-specific fields based on the selected role
                    if selected_role == 'entrepreneur':
                        role_application_data.update({
                            'company_name': role_specific_data.get('business_name', ''),
                            'project_stage': role_specific_data.get('business_stage', ''),
                            'industry': role_specific_data.get('industry', ''),
                            'project_description': role_specific_data.get('business_description', ''),
                            'funding_needed': role_specific_data.get('funding_needed', ''),
                            'team_size': role_specific_data.get('team_size', ''),
                            'support_needed': role_specific_data.get('support_needed', ''),
                            'previous_experience': role_specific_data.get('previous_experience', '')
                        })
                    elif selected_role == 'mentor':
                        role_application_data.update({
                            'expertise_areas': role_specific_data.get('expertise', ''),
                            'mentoring_experience': role_specific_data.get('experience', ''),
                            'availability': role_specific_data.get('availability', ''),
                            'preferred_communication': role_specific_data.get('preferred_communication', '')
                        })
                    elif selected_role == 'investor':
                        role_application_data.update({
                            'investment_range': role_specific_data.get('investment_range', ''),
                            'investment_stage': role_specific_data.get('investment_stage', ''),
                            'investment_focus': role_specific_data.get('preferred_industries', ''),
                            'due_diligence_requirements': role_specific_data.get('investment_criteria', ''),
                            'portfolio_companies': role_specific_data.get('portfolio_companies', '')
                        })
                    elif selected_role == 'community_member':
                        role_application_data.update({
                            'interests': role_specific_data.get('interests', ''),
                            'goals': role_specific_data.get('goals', '')
                        })

                    role_application = RoleApplication.objects.create(**role_application_data)
                    logger.info(f"Created role application for user {user.username} requesting {selected_role} role")

                # Store role additional info in profile if provided
                if role_additional_info:
                    profile.role_additional_info = role_additional_info
                    profile.save()

            except Exception as e:
                # Log any errors but don't fail registration
                logger.error(f"Error handling role assignment for user {user.username}: {e}")
                # Still save the role info in profile for manual review
                if role_additional_info:
                    profile.role_additional_info = role_additional_info
                    profile.requested_role_name = selected_role
                    profile.save()

        return user


class UserUpdateSerializer(serializers.ModelSerializer):
    """Serializer for updating user information"""
    profile = UserProfileSerializer(required=False)

    class Meta:
        model = User
        fields = ['first_name', 'last_name', 'email', 'profile']

    def update(self, instance, validated_data):
        profile_data = validated_data.pop('profile', {})
        
        # Update user fields
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()

        # Update profile fields
        if profile_data:
            profile = instance.profile
            for attr, value in profile_data.items():
                setattr(profile, attr, value)
            profile.save()

        return instance


class UserApprovalSerializer(serializers.ModelSerializer):
    """
    Serializer for UserApproval model with enhanced registration data
    """
    user_full_name = serializers.SerializerMethodField()
    user_email = serializers.SerializerMethodField()
    user_username = serializers.SerializerMethodField()
    days_pending = serializers.SerializerMethodField()
    requested_role_info = serializers.SerializerMethodField()
    role_specific_data = serializers.SerializerMethodField()
    profile_summary = serializers.SerializerMethodField()

    class Meta:
        model = UserApproval
        fields = [
            'id', 'user', 'status', 'created_at', 'updated_at',
            'reviewed_by', 'reviewed_at', 'rejection_reason', 'admin_notes',
            # Enhanced fields
            'user_full_name', 'user_email', 'user_username', 'days_pending',
            'requested_role_info', 'role_specific_data', 'profile_summary'
        ]
        read_only_fields = ['created_at', 'updated_at']

    def get_user_full_name(self, obj):
        """Get user's full name"""
        if obj.user.first_name and obj.user.last_name:
            return f"{obj.user.first_name} {obj.user.last_name}"
        return obj.user.username

    def get_user_email(self, obj):
        """Get user's email"""
        return obj.user.email

    def get_user_username(self, obj):
        """Get user's username"""
        return obj.user.username

    def get_days_pending(self, obj):
        """Calculate days since approval request was created"""
        if obj.status == 'pending':
            from django.utils import timezone
            delta = timezone.now() - obj.created_at
            return delta.days
        return None

    def get_requested_role_info(self, obj):
        """Get information about the requested role"""
        try:
            if hasattr(obj.user, 'profile') and obj.user.profile:
                profile = obj.user.profile

                # Get role applications
                role_applications = RoleApplication.objects.filter(
                    user=obj.user
                ).order_by('-created_at')

                if role_applications.exists():
                    latest_app = role_applications.first()
                    return {
                        'role_name': latest_app.requested_role.name,
                        'role_display_name': latest_app.requested_role.display_name,
                        'application_status': latest_app.status,
                        'motivation': latest_app.motivation,
                        'qualifications': latest_app.qualifications,
                        'applied_at': latest_app.created_at.isoformat() if latest_app.created_at else None
                    }

                # Fallback to profile requested role
                if hasattr(profile, 'requested_role_name') and profile.requested_role_name:
                    return {
                        'role_name': profile.requested_role_name,
                        'role_display_name': profile.requested_role_name.replace('_', ' ').title(),
                        'application_status': 'pending',
                        'motivation': getattr(profile, 'motivation', ''),
                        'qualifications': getattr(profile, 'qualifications', ''),
                        'applied_at': obj.created_at.isoformat() if obj.created_at else None
                    }
        except Exception as e:
            pass

        return None

    def get_role_specific_data(self, obj):
        """Get role-specific data from applications"""
        try:
            role_applications = RoleApplication.objects.filter(
                user=obj.user
            ).order_by('-created_at')

            if role_applications.exists():
                app = role_applications.first()
                role_name = app.requested_role.name

                if role_name == 'entrepreneur':
                    return {
                        'business_name': app.company_name,
                        'business_stage': app.project_stage,
                        'funding_needed': app.funding_needed,
                        'business_description': app.project_description,
                        'industry': app.industry,
                        'team_size': app.team_size,
                        'support_needed': ', '.join(app.support_needed) if isinstance(app.support_needed, list) else app.support_needed,
                        'previous_experience': app.previous_experience,
                        'experience': app.experience,
                        'portfolio_url': app.portfolio_url
                    }
                elif role_name == 'mentor':
                    return {
                        'expertise': ', '.join(app.expertise_areas) if isinstance(app.expertise_areas, list) else app.expertise_areas,
                        'mentor_experience': app.mentoring_experience,
                        'mentorship_areas': ', '.join(app.expertise_areas) if isinstance(app.expertise_areas, list) else app.expertise_areas,
                        'availability': app.availability,
                        'preferred_communication': ', '.join(app.preferred_communication) if isinstance(app.preferred_communication, list) else app.preferred_communication,
                        'experience': app.experience,
                        'portfolio_url': app.portfolio_url
                    }
                elif role_name == 'investor':
                    return {
                        'investment_range': app.investment_range,
                        'investment_stage': ', '.join(app.investment_stage) if isinstance(app.investment_stage, list) else app.investment_stage,
                        'preferred_industries': ', '.join(app.investment_focus) if isinstance(app.investment_focus, list) else app.investment_focus,
                        'investment_criteria': app.due_diligence_requirements,
                        'portfolio_companies': app.portfolio_companies,
                        'experience': app.experience,
                        'portfolio_url': app.portfolio_url
                    }
                elif role_name == 'user' or role_name == 'community_member':
                    return {
                        'interests': getattr(app, 'interests', ''),
                        'goals': getattr(app, 'goals', ''),
                        'experience': app.experience,
                        'portfolio_url': app.portfolio_url
                    }
        except Exception as e:
            pass

        return {}

    def get_profile_summary(self, obj):
        """Get user profile summary"""
        try:
            if hasattr(obj.user, 'profile') and obj.user.profile:
                profile = obj.user.profile
                return {
                    'location': profile.location,
                    'company': profile.company,
                    'job_title': profile.job_title,
                    'industry': profile.industry,
                    'phone_number': profile.phone_number,
                    'bio': profile.bio,
                    'language': profile.language,
                    'website': profile.website,
                    'linkedin_url': profile.linkedin_url,
                    'experience_years': profile.experience_years,
                    'created_at': profile.created_at.isoformat() if profile.created_at else None
                }
        except Exception as e:
            pass

        return {}
