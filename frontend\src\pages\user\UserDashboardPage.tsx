import React from 'react';
import { UnifiedDashboard } from '../../components/dashboard/unified';

/**
 * ✅ CONSOLIDATED USER DASHBOARD PAGE
 *
 * Now uses UnifiedDashboard with user role configuration.
 * Provides general user tools through unified architecture.
 *
 * Key Features:
 * - Unified dashboard with user privileges
 * - Personal profile and settings management
 * - Community participation and networking
 * - Learning resources and content access
 * - Basic analytics and progress tracking
 * - Consistent UI with role-specific sections
 * - Maintainable single-source architecture
 */
const UserDashboardPage: React.FC = () => {
  return <UnifiedDashboard role="user" />;
};

export default UserDashboardPage;
