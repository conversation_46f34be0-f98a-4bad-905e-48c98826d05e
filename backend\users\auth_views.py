"""
Dedicated authentication views
Clean API views that match frontend expectations
"""
from rest_framework import status
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import AllowAny, IsAuthenticated
from django.contrib.auth import authenticate, login, logout
from django.contrib.auth.models import User
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from rest_framework_simplejwt.tokens import RefreshToken
from .serializers import UserSerializer, EnhancedRegistrationSerializer
from .models.email_verification import EmailVerificationToken, EmailVerificationSettings
import logging

logger = logging.getLogger(__name__)


class LoginView(APIView):
    """
    Login endpoint that matches frontend expectations
    POST /api/auth/login/
    """
    permission_classes = [AllowAny]
    
    def post(self, request):
        username = request.data.get('username')
        password = request.data.get('password')
        
        if not username or not password:
            return Response(
                {'error': 'Username and password are required'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Authenticate user
        user = authenticate(username=username, password=password)

        if user:
            # Check if user account is active
            if not user.is_active:
                # Check approval status
                try:
                    from .models import UserApproval
                    approval = UserApproval.objects.get(user=user)

                    if approval.status == 'pending':
                        # Calculate days pending
                        from django.utils import timezone
                        days_pending = (timezone.now() - approval.created_at).days

                        # Create more informative message
                        if days_pending == 0:
                            time_info = "today"
                        elif days_pending == 1:
                            time_info = "1 day ago"
                        else:
                            time_info = f"{days_pending} days ago"

                        message = f"Your account is pending admin approval (submitted {time_info}). You will receive an email once your account is approved. Typical approval time is 1-3 business days."

                        logger.info(f"Login attempt by pending user: {username} (pending for {days_pending} days)")
                        return Response(
                            {
                                'error': 'Account pending approval',
                                'message': message,
                                'status': 'pending',
                                'days_pending': days_pending,
                                'submitted_at': approval.created_at.isoformat()
                            },
                            status=status.HTTP_403_FORBIDDEN
                        )
                    elif approval.status == 'rejected':
                        logger.info(f"Login attempt by rejected user: {username}")
                        return Response(
                            {
                                'error': 'Account rejected',
                                'message': 'Your account registration was not approved. Please contact support for more information.',
                                'status': 'rejected'
                            },
                            status=status.HTTP_403_FORBIDDEN
                        )
                except UserApproval.DoesNotExist:
                    # No approval record found, account is inactive for other reasons
                    logger.warning(f"Login attempt by inactive user without approval record: {username}")
                    return Response(
                        {
                            'error': 'Account inactive',
                            'message': 'Your account is inactive. Please contact support.'
                        },
                        status=status.HTTP_403_FORBIDDEN
                    )

            # Generate JWT tokens
            refresh = RefreshToken.for_user(user)
            access_token = refresh.access_token

            # Serialize user data
            user_serializer = UserSerializer(user)

            response_data = {
                'user': user_serializer.data,
                'access': str(access_token),
                'refresh': str(refresh),
            }

            logger.info(f"User {username} logged in successfully")
            return Response(response_data, status=status.HTTP_200_OK)
        else:
            logger.warning(f"Failed login attempt for username: {username}")
            return Response(
                {'error': 'Invalid credentials'},
                status=status.HTTP_400_BAD_REQUEST
            )


class LogoutView(APIView):
    """
    Logout endpoint
    POST /api/auth/logout/
    """
    permission_classes = [IsAuthenticated]
    
    def post(self, request):
        try:
            # Get the refresh token from request
            refresh_token = request.data.get('refresh')
            if refresh_token:
                token = RefreshToken(refresh_token)
                token.blacklist()
            
            # Django logout
            logout(request)
            
            logger.info(f"User {request.user.username} logged out successfully")
            return Response(
                {'message': 'Successfully logged out'},
                status=status.HTTP_200_OK
            )
        except Exception as e:
            logger.error(f"Logout error: {e}")
            return Response(
                {'message': 'Logged out'},
                status=status.HTTP_200_OK
            )





class EnhancedRegisterView(APIView):
    """
    Enhanced registration endpoint with comprehensive role-based data collection
    POST /api/auth/register-enhanced/
    """
    permission_classes = [AllowAny]

    def post(self, request):
        serializer = EnhancedRegistrationSerializer(data=request.data)

        if serializer.is_valid():
            try:
                user = serializer.save()

                # Check if user needs approval based on role
                selected_role = request.data.get('selected_role', '').lower()
                requires_approval = selected_role not in ['user', '']

                # Set user active status based on approval requirement
                if not requires_approval:
                    user.is_active = True
                    user.save()

                # Serialize user data
                user_serializer = UserSerializer(user)

                # Create appropriate response based on approval requirement
                if requires_approval:
                    response_data = {
                        'user': user_serializer.data,
                        'message': 'Registration successful. Your account is pending admin approval. You will receive an email once approved.',
                        'status': 'pending_approval',
                        'requires_approval': True
                    }
                else:
                    response_data = {
                        'user': user_serializer.data,
                        'message': 'Registration successful. Your account is ready to use!',
                        'status': 'active',
                        'requires_approval': False
                    }

                logger.info(f"New user registered with enhanced data (pending approval): {user.username}")
                return Response(response_data, status=status.HTTP_201_CREATED)

            except Exception as e:
                logger.error(f"Enhanced registration error: {e}", exc_info=True)
                return Response(
                    {
                        'error': 'Registration failed. Please try again.',
                        'message': f'Internal server error: {str(e)}',
                        'details': str(e)
                    },
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR
                )
        else:
            # Log validation errors for debugging
            logger.error(f"Enhanced registration validation errors: {serializer.errors}")

            # Create a user-friendly error message
            error_messages = []
            for field, errors in serializer.errors.items():
                if isinstance(errors, list):
                    for error in errors:
                        error_messages.append(f"{field}: {error}")
                else:
                    error_messages.append(f"{field}: {errors}")

            return Response(
                {
                    'errors': serializer.errors,
                    'message': 'Registration failed: ' + '; '.join(error_messages) if error_messages else 'Invalid data provided'
                },
                status=status.HTTP_400_BAD_REQUEST
            )


class CheckUsernameView(APIView):
    """
    Check username availability endpoint
    POST /api/auth/check-username/
    """
    permission_classes = [AllowAny]

    def post(self, request):
        username = request.data.get('username', '').strip()

        if not username:
            return Response(
                {'available': False, 'message': 'Username is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        if len(username) < 3:
            return Response(
                {'available': False, 'message': 'Username must be at least 3 characters'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Check if username exists
        exists = User.objects.filter(username=username).exists()

        return Response(
            {
                'available': not exists,
                'message': 'Username is available' if not exists else 'Username is already taken'
            },
            status=status.HTTP_200_OK
        )


class CheckEmailView(APIView):
    """
    Check email availability endpoint
    POST /api/auth/check-email/
    """
    permission_classes = [AllowAny]

    def post(self, request):
        email = request.data.get('email', '').strip()

        if not email:
            return Response(
                {'available': False, 'message': 'Email is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Basic email validation
        import re
        email_regex = r'^[^\s@]+@[^\s@]+\.[^\s@]+$'
        if not re.match(email_regex, email):
            return Response(
                {'available': False, 'message': 'Invalid email format'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Check if email exists
        exists = User.objects.filter(email=email).exists()

        return Response(
            {
                'available': not exists,
                'message': 'Email is available' if not exists else 'Email is already registered'
            },
            status=status.HTTP_200_OK
        )


class UserView(APIView):
    """
    Get current user endpoint
    GET /api/auth/user/
    """
    permission_classes = [IsAuthenticated]

    def get(self, request):
        serializer = UserSerializer(request.user)
        return Response(serializer.data, status=status.HTTP_200_OK)
