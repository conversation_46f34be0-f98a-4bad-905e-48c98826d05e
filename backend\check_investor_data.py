from django.contrib.auth.models import User
from users.models import UserProfile, RoleApplication

# Find the latest user
latest_user = User.objects.order_by('-date_joined').first()

if latest_user:
    print(f"Found user: {latest_user.username}")
    print(f"Email: {latest_user.email}")

    # Check UserProfile
    if hasattr(latest_user, 'profile'):
        profile = latest_user.profile
        print(f"Portfolio companies in profile: {getattr(profile, 'portfolio_companies', 'Field not found')}")
        print(f"Investment range in profile: {getattr(profile, 'investment_range', 'Field not found')}")
        print(f"Investment criteria in profile: {getattr(profile, 'investment_criteria', 'Field not found')}")
        print(f"Preferred industries in profile: {getattr(profile, 'preferred_industries', 'Field not found')}")
    else:
        print("No profile found")

    # Check RoleApplication
    role_app = RoleApplication.objects.filter(user=latest_user).first()
    if role_app:
        print(f"Requested role: {role_app.requested_role}")
        print(f"Portfolio companies in role app: {getattr(role_app, 'portfolio_companies', 'Field not found')}")
        print(f"Investment range in role app: {getattr(role_app, 'investment_range', 'Field not found')}")
    else:
        print("No role application found")
else:
    print("No users found")
