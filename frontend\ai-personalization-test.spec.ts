/**
 * 🎯 AI PERSONALIZATION E2E TEST
 * Tests AI personalization with and without authentication
 */

import { test, expect } from '@playwright/test';

test.describe('🤖 AI Personalization Tests', () => {
  
  test('🔓 Should work for anonymous users with generic responses', async ({ page }) => {
    console.log('🧪 Testing AI for anonymous users...');
    
    await page.goto('/ai-chat');
    await page.waitForLoadState('load');
    
    // Wait for AI to load
    await page.waitForTimeout(3000);
    
    // Check if page loads
    await expect(page.locator('body')).toBeVisible();
    
    // Look for generic welcome message
    const pageContent = await page.textContent('body');
    const hasGenericWelcome = pageContent?.includes('المستخدم') || 
                             pageContent?.includes('صديقي') ||
                             pageContent?.includes('أهلاً وسهلاً');
    
    if (hasGenericWelcome) {
      console.log('✅ Anonymous user gets generic welcome message');
    }
    
    console.log('✅ Anonymous AI chat test completed');
  });

  test('🔐 Should provide personalized responses for authenticated users', async ({ page }) => {
    console.log('🧪 Testing AI personalization for authenticated users...');
    
    // Login first
    await page.goto('/login');
    await page.waitForLoadState('load');
    
    // Fill login form
    await page.fill('input[name="username"], input[type="text"]', 'admin');
    await page.fill('input[name="password"], input[type="password"]', 'admin123');
    
    // Submit form
    await page.keyboard.press('Enter');
    
    // Wait for redirect
    await page.waitForTimeout(2000);
    
    // Navigate to AI chat
    await page.goto('/ai-chat');
    await page.waitForLoadState('load');
    
    // Wait for AI to load
    await page.waitForTimeout(3000);
    
    // Check for personalized content
    const pageContent = await page.textContent('body');
    const hasPersonalizedContent = pageContent?.includes('admin') ||
                                  pageContent?.includes('مدير') ||
                                  pageContent?.includes('أهلاً وسهلاً');
    
    if (hasPersonalizedContent) {
      console.log('✅ Authenticated user gets personalized responses');
    }
    
    console.log('✅ Authenticated AI chat test completed');
  });

  test('🌍 Should demonstrate regional dialect switching', async ({ page }) => {
    console.log('🧪 Testing regional dialect switching...');
    
    await page.goto('/ai-chat');
    await page.waitForLoadState('load');
    
    // Wait for AI to load
    await page.waitForTimeout(3000);
    
    // Look for regional selector
    const regionButton = page.locator('button:has-text("دمشق"), button:has-text("Damascus")');
    if (await regionButton.isVisible()) {
      console.log('✅ Regional selector found');
      
      // Try clicking to change region
      await regionButton.click();
      await page.waitForTimeout(1000);
      
      console.log('✅ Regional dialect switching available');
    }
    
    console.log('✅ Regional dialect test completed');
  });

  test('🎯 Should demonstrate chat type switching', async ({ page }) => {
    console.log('🧪 Testing chat type switching...');
    
    await page.goto('/ai-chat');
    await page.waitForLoadState('load');
    
    // Wait for AI to load
    await page.waitForTimeout(3000);
    
    // Look for chat type selector
    const chatTypeSelector = page.locator('select, combobox');
    if (await chatTypeSelector.isVisible()) {
      console.log('✅ Chat type selector found');
      
      // Check available options
      const options = await page.locator('option').allTextContents();
      console.log(`Available chat types: ${options.join(', ')}`);
      
      console.log('✅ Chat type switching available');
    }
    
    console.log('✅ Chat type test completed');
  });

  test('💬 Should demonstrate message sending capability', async ({ page }) => {
    console.log('🧪 Testing message sending...');
    
    await page.goto('/ai-chat');
    await page.waitForLoadState('load');
    
    // Wait for AI to load
    await page.waitForTimeout(3000);
    
    // Look for input field
    const inputField = page.locator('input[type="text"], textarea');
    if (await inputField.isVisible()) {
      console.log('✅ Message input field found');
      
      // Type a test message
      await inputField.fill('مرحبا ياسمين');
      
      // Look for send button
      const sendButton = page.locator('button:has-text("Send"), button[type="submit"]');
      if (await sendButton.isVisible() && !await sendButton.isDisabled()) {
        console.log('✅ Send button is available and enabled');
      }
      
      console.log('✅ Message sending interface working');
    }
    
    console.log('✅ Message sending test completed');
  });

  test('📱 Should work on mobile devices', async ({ page }) => {
    console.log('🧪 Testing mobile AI chat...');
    
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    
    await page.goto('/ai-chat');
    await page.waitForLoadState('load');
    
    // Wait for AI to load
    await page.waitForTimeout(3000);
    
    // Check if page is responsive
    const body = page.locator('body');
    await expect(body).toBeVisible();
    
    const bodyBox = await body.boundingBox();
    expect(bodyBox?.width).toBeLessThanOrEqual(375);
    
    console.log('✅ Mobile AI chat working');
  });

  test('⚡ Should meet performance benchmarks', async ({ page }) => {
    console.log('🧪 Testing AI chat performance...');
    
    const startTime = Date.now();
    
    await page.goto('/ai-chat');
    await page.waitForLoadState('load');
    
    const loadTime = Date.now() - startTime;
    
    // Should load within 5 seconds
    expect(loadTime).toBeLessThan(5000);
    
    console.log(`✅ AI chat loaded in ${loadTime}ms (under 5s)`);
  });

  test('🔄 Should handle navigation between features', async ({ page }) => {
    console.log('🧪 Testing AI feature navigation...');
    
    // Test navigation between AI and other features
    await page.goto('/ai-chat');
    await page.waitForLoadState('load');
    await expect(page.locator('body')).toBeVisible();
    
    // Navigate to home
    await page.goto('/');
    await page.waitForLoadState('load');
    await expect(page.locator('body')).toBeVisible();
    
    // Navigate back to AI chat
    await page.goto('/ai-chat');
    await page.waitForLoadState('load');
    await expect(page.locator('body')).toBeVisible();
    
    console.log('✅ AI navigation working');
  });

});
