/**
 * Enhanced User Approval Manager Component
 * Super Admin interface for managing user registrations and approvals
 * Uses glass morphism design and displays complete registration data
 */

import React, { useState, useEffect } from 'react';
import { useLanguage } from '../../hooks/useLanguage';
import {
  Clock,
  CheckCircle,
  XCircle,
  Mail,
  User,
  Calendar,
  AlertCircle,
  Search,
  Filter,
  RefreshCw,
  Eye,
  MessageSquare,
  MapPin,
  Building,
  Briefcase,
  Phone,
  Globe,
  Users,
  DollarSign,
  Target,
  Award,
  TrendingUp,
  Heart,
  Star,
  ChevronDown,
  ChevronUp
} from 'lucide-react';

interface UserApproval {
  id: string;
  user: {
    id: string;
    username: string;
    email: string;
    first_name: string;
    last_name: string;
    date_joined: string;
  };
  status: 'pending' | 'approved' | 'rejected';
  reviewed_by?: {
    username: string;
    first_name: string;
    last_name: string;
  };
  reviewed_at?: string;
  rejection_reason?: string;
  admin_notes?: string;
  created_at: string;
  updated_at: string;
  user_full_name: string;
  days_pending?: number;
  // Enhanced registration data
  requested_role_info?: {
    role_name: string;
    role_display_name: string;
    application_status: string;
    motivation: string;
    qualifications: string;
    applied_at: string;
  };
  role_specific_data?: {
    // General fields (available for all roles)
    experience?: string;
    portfolio_url?: string;
    // Entrepreneur fields
    business_name?: string;
    business_stage?: string;
    funding_needed?: string;
    business_description?: string;
    industry?: string;
    team_size?: string;
    support_needed?: string;
    previous_experience?: string;
    // Mentor fields
    expertise?: string;
    mentor_experience?: string;
    mentorship_areas?: string;
    availability?: string;
    preferred_communication?: string;
    // Investor fields
    investment_range?: string;
    investment_stage?: string;
    preferred_industries?: string;
    investment_criteria?: string;
    portfolio_companies?: string;
    // User fields
    interests?: string;
    goals?: string;
  };
  profile_summary?: {
    location?: string;
    company?: string;
    job_title?: string;
    industry?: string;
    phone_number?: string;
    bio?: string;
    language?: string;
    website?: string;
    linkedin_url?: string;
  };
}

interface ApprovalStats {
  total: number;
  pending: number;
  approved: number;
  rejected: number;
}

const UserApprovalManager: React.FC = () => {
  const { language, isRTL } = useLanguage();
  const [approvals, setApprovals] = useState<UserApproval[]>([]);
  const [stats, setStats] = useState<ApprovalStats>({ total: 0, pending: 0, approved: 0, rejected: 0 });
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState<'all' | 'pending' | 'approved' | 'rejected'>('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedApproval, setSelectedApproval] = useState<UserApproval | null>(null);
  const [rejectionReason, setRejectionReason] = useState('');
  const [showRejectModal, setShowRejectModal] = useState(false);
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [detailedUserData, setDetailedUserData] = useState<any>(null);
  const [expandedRows, setExpandedRows] = useState<Set<string>>(new Set());

  useEffect(() => {
    fetchApprovals();
    fetchStats();
  }, [filter]);

  const toggleRowExpansion = (approvalId: string) => {
    const newExpandedRows = new Set(expandedRows);
    if (newExpandedRows.has(approvalId)) {
      newExpandedRows.delete(approvalId);
    } else {
      newExpandedRows.add(approvalId);
    }
    setExpandedRows(newExpandedRows);
  };

  const fetchDetailedUserData = async (approvalId: string) => {
    try {
      const response = await fetch(`/api/users/approvals/${approvalId}/detailed_review/`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('yasmeen_auth_token')}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        setDetailedUserData(data);
        setShowDetailModal(true);
      }
    } catch (error) {
      console.error('Error fetching detailed user data:', error);
    }
  };

  const getRoleIcon = (roleName?: string) => {
    switch (roleName) {
      case 'entrepreneur':
        return <Briefcase className="w-4 h-4 text-blue-500" />;
      case 'mentor':
        return <Users className="w-4 h-4 text-green-500" />;
      case 'investor':
        return <DollarSign className="w-4 h-4 text-purple-500" />;
      case 'user':
        return <User className="w-4 h-4 text-gray-500" />;
      default:
        return <User className="w-4 h-4 text-gray-400" />;
    }
  };

  const getRoleColor = (roleName?: string) => {
    switch (roleName) {
      case 'entrepreneur':
        return 'bg-blue-500/20 text-blue-300 border-blue-400/30';
      case 'mentor':
        return 'bg-green-500/20 text-green-300 border-green-400/30';
      case 'investor':
        return 'bg-purple-500/20 text-purple-300 border-purple-400/30';
      case 'user':
        return 'bg-gray-500/20 text-gray-300 border-gray-400/30';
      default:
        return 'bg-gray-500/20 text-gray-300 border-gray-400/30';
    }
  };

  const fetchApprovals = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/users/approvals/', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('yasmeen_auth_token')}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        console.log('📊 Raw approvals data:', data);
        // Handle both array and paginated response formats
        const approvals = Array.isArray(data) ? data : (data.results || []);
        const filteredData = filter === 'all' ? approvals : approvals.filter((approval: UserApproval) => approval.status === filter);
        setApprovals(filteredData);
        console.log('📊 Filtered approvals:', filteredData);
      } else {
        console.error('❌ Failed to fetch approvals:', response.status, response.statusText);
        if (response.status === 401) {
          console.error('❌ Unauthorized - user may not have admin permissions');
        }
      }
    } catch (error) {
      console.error('❌ Error fetching approvals:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      // Calculate stats from approvals data instead of separate endpoint
      const response = await fetch('/api/users/approvals/', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('yasmeen_auth_token')}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        const approvals = Array.isArray(data) ? data : (data.results || []);

        const stats = {
          total: approvals.length,
          pending: approvals.filter((a: UserApproval) => a.status === 'pending').length,
          approved: approvals.filter((a: UserApproval) => a.status === 'approved').length,
          rejected: approvals.filter((a: UserApproval) => a.status === 'rejected').length,
        };

        setStats(stats);
        console.log('📊 Calculated stats:', stats);
      } else {
        console.error('❌ Failed to fetch stats:', response.status, response.statusText);
      }
    } catch (error) {
      console.error('❌ Error fetching stats:', error);
    }
  };

  const handleApprove = async (approvalId: string) => {
    try {
      const response = await fetch(`/api/users/approvals/${approvalId}/approve/`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('yasmeen_auth_token')}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        await fetchApprovals();
        await fetchStats();
      }
    } catch (error) {
      console.error('Error approving user:', error);
    }
  };

  const handleReject = async () => {
    if (!selectedApproval || !rejectionReason.trim()) return;

    try {
      const response = await fetch(`/api/users/approvals/${selectedApproval.id}/reject/`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('yasmeen_auth_token')}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ reason: rejectionReason }),
      });

      if (response.ok) {
        await fetchApprovals();
        await fetchStats();
        setShowRejectModal(false);
        setRejectionReason('');
        setSelectedApproval(null);
      }
    } catch (error) {
      console.error('Error rejecting user:', error);
    }
  };

  const openRejectModal = (approval: UserApproval) => {
    setSelectedApproval(approval);
    setShowRejectModal(true);
  };

  const filteredApprovals = approvals.filter(approval => {
    if (!approval) return false;
    const searchLower = (searchTerm || '').toLowerCase();
    const username = (approval.user?.username || '').toLowerCase();
    const email = (approval.user?.email || '').toLowerCase();
    const fullName = (approval.user_full_name || '').toLowerCase();

    return username.includes(searchLower) ||
           email.includes(searchLower) ||
           fullName.includes(searchLower);
  });

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="w-5 h-5 text-yellow-500" />;
      case 'approved':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'rejected':
        return <XCircle className="w-5 h-5 text-red-500" />;
      default:
        return <AlertCircle className="w-5 h-5 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-500/20 text-yellow-300 border-yellow-400/30';
      case 'approved':
        return 'bg-green-500/20 text-green-300 border-green-400/30';
      case 'rejected':
        return 'bg-red-500/20 text-red-300 border-red-400/30';
      default:
        return 'bg-gray-500/20 text-gray-300 border-gray-400/30';
    }
  };

  return (
    <div className={`${isRTL ? 'rtl' : 'ltr'}`} dir={isRTL ? 'rtl' : 'ltr'}>
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-white mb-2 font-arabic">
          {language === 'ar' ? 'إدارة موافقات المستخدمين' : 'User Approval Management'}
        </h1>
        <p className="text-gray-300">
          {language === 'ar'
            ? 'راجع واعتمد أو ارفض طلبات التسجيل الجديدة'
            : 'Review and approve or reject new user registrations'
          }
        </p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div className="glass-morphism p-6 hover:bg-white/10 transition-all duration-300">
          <div className="flex items-center">
            <div className="p-2 bg-blue-500/20 border border-blue-400/30 rounded-lg">
              <User className="w-6 h-6 text-blue-400" />
            </div>
            <div className={`${isRTL ? 'mr-4' : 'ml-4'}`}>
              <p className="text-sm font-medium text-blue-300 font-arabic">
                {language === 'ar' ? 'إجمالي الطلبات' : 'Total Requests'}
              </p>
              <p className="text-2xl font-bold text-white">{stats.total}</p>
            </div>
          </div>
        </div>

        <div className="glass-morphism p-6 hover:bg-white/10 transition-all duration-300">
          <div className="flex items-center">
            <div className="p-2 bg-yellow-500/20 border border-yellow-400/30 rounded-lg">
              <Clock className="w-6 h-6 text-yellow-400" />
            </div>
            <div className={`${isRTL ? 'mr-4' : 'ml-4'}`}>
              <p className="text-sm font-medium text-yellow-300 font-arabic">
                {language === 'ar' ? 'في الانتظار' : 'Pending'}
              </p>
              <p className="text-2xl font-bold text-white">{stats.pending}</p>
            </div>
          </div>
        </div>

        <div className="glass-morphism p-6 hover:bg-white/10 transition-all duration-300">
          <div className="flex items-center">
            <div className="p-2 bg-green-500/20 border border-green-400/30 rounded-lg">
              <CheckCircle className="w-6 h-6 text-green-400" />
            </div>
            <div className={`${isRTL ? 'mr-4' : 'ml-4'}`}>
              <p className="text-sm font-medium text-green-300 font-arabic">
                {language === 'ar' ? 'معتمد' : 'Approved'}
              </p>
              <p className="text-2xl font-bold text-white">{stats.approved}</p>
            </div>
          </div>
        </div>

        <div className="glass-morphism p-6 hover:bg-white/10 transition-all duration-300">
          <div className="flex items-center">
            <div className="p-2 bg-red-500/20 border border-red-400/30 rounded-lg">
              <XCircle className="w-6 h-6 text-red-400" />
            </div>
            <div className={`${isRTL ? 'mr-4' : 'ml-4'}`}>
              <p className="text-sm font-medium text-red-300 font-arabic">
                {language === 'ar' ? 'مرفوض' : 'Rejected'}
              </p>
              <p className="text-2xl font-bold text-white">{stats.rejected}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="glass-light p-6 mb-6">
        <div className={`flex flex-col md:flex-row gap-4 ${isRTL ? 'md:flex-row-reverse' : ''}`}>
          {/* Filter Buttons */}
          <div className="flex gap-2">
            {(['all', 'pending', 'approved', 'rejected'] as const).map((filterOption) => (
              <button
                key={filterOption}
                onClick={() => setFilter(filterOption)}
                className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300 font-arabic ${
                  filter === filterOption
                    ? 'bg-blue-500 text-white border border-blue-500'
                    : 'bg-white/20 text-gray-300 hover:bg-white/30 border border-white/30'
                }`}
              >
                {language === 'ar' ? {
                  all: 'الكل',
                  pending: 'في الانتظار',
                  approved: 'معتمد',
                  rejected: 'مرفوض'
                }[filterOption] : {
                  all: 'All',
                  pending: 'Pending',
                  approved: 'Approved',
                  rejected: 'Rejected'
                }[filterOption]}
              </button>
            ))}
          </div>

          {/* Search */}
          <div className="flex-1 relative">
            <Search className={`absolute top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400 ${isRTL ? 'right-3' : 'left-3'}`} />
            <input
              type="text"
              placeholder={language === 'ar' ? 'البحث بالاسم أو البريد الإلكتروني...' : 'Search by name or email...'}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className={`w-full bg-white/20 border border-white/30 rounded-lg py-2 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 font-arabic ${
                isRTL ? 'pr-10 pl-4' : 'pl-10 pr-4'
              }`}
              dir={isRTL ? 'rtl' : 'ltr'}
            />
          </div>

          {/* Refresh Button */}
          <button
            onClick={() => { fetchApprovals(); fetchStats(); }}
            className="px-4 py-2 bg-white/20 border border-white/30 text-gray-300 rounded-lg hover:bg-white/30 transition-all duration-300 flex items-center gap-2 font-arabic"
          >
            <RefreshCw className="w-4 h-4" />
            {language === 'ar' ? 'تحديث' : 'Refresh'}
          </button>
        </div>
      </div>

      {/* Approvals Table */}
      <div className="glass-morphism overflow-hidden">
        {loading ? (
          <div className="p-8 text-center">
            <RefreshCw className="w-8 h-8 text-gray-400 animate-spin mx-auto mb-4" />
            <p className="text-gray-300">
              {language === 'ar' ? 'جاري التحميل...' : 'Loading...'}
            </p>
          </div>
        ) : filteredApprovals.length === 0 ? (
          <div className="p-8 text-center">
            <User className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-300">
              {language === 'ar' ? 'لا توجد طلبات موافقة' : 'No approval requests found'}
            </p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-white/10 border-b border-white/20">
                <tr>
                  <th className={`px-6 py-3 text-xs font-medium text-gray-300 uppercase tracking-wider ${isRTL ? 'text-right' : 'text-left'}`}>
                    {language === 'ar' ? 'المستخدم' : 'User'}
                  </th>
                  <th className={`px-6 py-3 text-xs font-medium text-gray-300 uppercase tracking-wider ${isRTL ? 'text-right' : 'text-left'}`}>
                    {language === 'ar' ? 'الحالة' : 'Status'}
                  </th>
                  <th className={`px-6 py-3 text-xs font-medium text-gray-300 uppercase tracking-wider ${isRTL ? 'text-right' : 'text-left'}`}>
                    {language === 'ar' ? 'تاريخ التسجيل' : 'Registration Date'}
                  </th>
                  <th className={`px-6 py-3 text-xs font-medium text-gray-300 uppercase tracking-wider ${isRTL ? 'text-right' : 'text-left'}`}>
                    {language === 'ar' ? 'الإجراءات' : 'Actions'}
                  </th>
                </tr>
              </thead>
              <tbody className="bg-transparent divide-y divide-white/20">
                {filteredApprovals.map((approval) => (
                  <React.Fragment key={approval.id}>
                    <tr className="hover:bg-white/10 transition-colors duration-200">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-10 w-10">
                            <div className="h-10 w-10 rounded-full bg-white/20 flex items-center justify-center">
                              <User className="w-5 h-5 text-gray-300" />
                            </div>
                          </div>
                          <div className={`${isRTL ? 'mr-4' : 'ml-4'}`}>
                            <div className="text-sm font-medium text-white">
                              {approval.user_full_name}
                            </div>
                            <div className="text-sm text-gray-300">
                              {approval.user.email}
                            </div>
                            {approval.profile_summary?.location && (
                              <div className="text-xs text-gray-400 flex items-center mt-1">
                                <MapPin className="w-3 h-3 mr-1" />
                                {approval.profile_summary.location}
                              </div>
                            )}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {approval.requested_role_info ? (
                          <div className="flex items-center">
                            {getRoleIcon(approval.requested_role_info.role_name)}
                            <span className={`${isRTL ? 'mr-2' : 'ml-2'} px-2 py-1 text-xs font-semibold rounded-full border ${getRoleColor(approval.requested_role_info.role_name)}`}>
                              {approval.requested_role_info.role_display_name}
                            </span>
                          </div>
                        ) : (
                          <span className="text-gray-400 text-sm">
                            {language === 'ar' ? 'غير محدد' : 'Not specified'}
                          </span>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          {getStatusIcon(approval.status)}
                          <span className={`${isRTL ? 'mr-2' : 'ml-2'} px-2 py-1 text-xs font-semibold rounded-full border ${getStatusColor(approval.status)}`}>
                            {language === 'ar' ? {
                              pending: 'في الانتظار',
                              approved: 'معتمد',
                              rejected: 'مرفوض'
                            }[approval.status] : {
                              pending: 'Pending',
                              approved: 'Approved',
                              rejected: 'Rejected'
                            }[approval.status]}
                          </span>
                          {approval.days_pending && approval.days_pending > 0 && (
                            <span className={`${isRTL ? 'mr-2' : 'ml-2'} text-xs text-gray-400`}>
                              ({approval.days_pending} {language === 'ar' ? 'يوم' : 'days'})
                            </span>
                          )}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                        {new Date(approval.created_at).toLocaleDateString(language === 'ar' ? 'ar-SA' : 'en-US')}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex items-center gap-2">
                          {/* View Details Button */}
                          <button
                            onClick={() => toggleRowExpansion(approval.id)}
                            className="inline-flex items-center px-2 py-1 border border-white/30 text-xs font-medium rounded-md text-gray-300 bg-white/20 hover:bg-white/30 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500"
                          >
                            {expandedRows.has(approval.id) ? (
                              <ChevronUp className="w-4 h-4" />
                            ) : (
                              <ChevronDown className="w-4 h-4" />
                            )}
                            {language === 'ar' ? 'التفاصيل' : 'Details'}
                          </button>

                          {approval.status === 'pending' && (
                            <>
                              <button
                                onClick={() => handleApprove(approval.id)}
                                className="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                              >
                                <CheckCircle className="w-4 h-4 mr-1" />
                                {language === 'ar' ? 'اعتماد' : 'Approve'}
                              </button>
                              <button
                                onClick={() => openRejectModal(approval)}
                                className="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                              >
                                <XCircle className="w-4 h-4 mr-1" />
                                {language === 'ar' ? 'رفض' : 'Reject'}
                              </button>
                            </>
                          )}

                          {approval.status !== 'pending' && (
                            <span className="text-gray-400">
                              {language === 'ar' ? 'تم المراجعة' : 'Reviewed'}
                            </span>
                          )}
                        </div>
                      </td>
                    </tr>

                    {/* Expandable Row Details */}
                    {expandedRows.has(approval.id) && (
                      <tr>
                        <td colSpan={5} className="px-6 py-4 bg-gray-50">
                          <div className="space-y-4">
                            {/* Profile Summary */}
                            {approval.profile_summary && (
                              <div>
                                <h4 className="text-sm font-medium text-gray-900 mb-2 flex items-center">
                                  <User className="w-4 h-4 mr-2" />
                                  {language === 'ar' ? 'معلومات الملف الشخصي' : 'Profile Information'}
                                </h4>
                                <div className="grid grid-cols-2 gap-4 text-sm">
                                  {approval.profile_summary.company && (
                                    <div className="flex items-center">
                                      <Building className="w-4 h-4 mr-2 text-gray-400" />
                                      <span className="text-gray-600">{language === 'ar' ? 'الشركة:' : 'Company:'}</span>
                                      <span className="ml-2 text-gray-900">{approval.profile_summary.company}</span>
                                    </div>
                                  )}
                                  {approval.profile_summary.job_title && (
                                    <div className="flex items-center">
                                      <Briefcase className="w-4 h-4 mr-2 text-gray-400" />
                                      <span className="text-gray-600">{language === 'ar' ? 'المنصب:' : 'Job Title:'}</span>
                                      <span className="ml-2 text-gray-900">{approval.profile_summary.job_title}</span>
                                    </div>
                                  )}
                                  {approval.profile_summary.location && (
                                    <div className="flex items-center">
                                      <MapPin className="w-4 h-4 mr-2 text-gray-400" />
                                      <span className="text-gray-600">{language === 'ar' ? 'الموقع:' : 'Location:'}</span>
                                      <span className="ml-2 text-gray-900">{approval.profile_summary.location}</span>
                                    </div>
                                  )}
                                  {approval.profile_summary.industry && (
                                    <div className="flex items-center">
                                      <Building className="w-4 h-4 mr-2 text-gray-400" />
                                      <span className="text-gray-600">{language === 'ar' ? 'الصناعة:' : 'Industry:'}</span>
                                      <span className="ml-2 text-gray-900">{approval.profile_summary.industry}</span>
                                    </div>
                                  )}
                                  {approval.profile_summary.phone_number && (
                                    <div className="flex items-center">
                                      <Phone className="w-4 h-4 mr-2 text-gray-400" />
                                      <span className="text-gray-600">{language === 'ar' ? 'الهاتف:' : 'Phone:'}</span>
                                      <span className="ml-2 text-gray-900">{approval.profile_summary.phone_number}</span>
                                    </div>
                                  )}
                                  {approval.profile_summary.experience_years && (
                                    <div className="flex items-center">
                                      <Clock className="w-4 h-4 mr-2 text-gray-400" />
                                      <span className="text-gray-600">{language === 'ar' ? 'سنوات الخبرة:' : 'Experience Years:'}</span>
                                      <span className="ml-2 text-gray-900">{approval.profile_summary.experience_years}</span>
                                    </div>
                                  )}
                                  {approval.profile_summary.language && (
                                    <div className="flex items-center">
                                      <Globe className="w-4 h-4 mr-2 text-gray-400" />
                                      <span className="text-gray-600">{language === 'ar' ? 'اللغة:' : 'Language:'}</span>
                                      <span className="ml-2 text-gray-900">{approval.profile_summary.language}</span>
                                    </div>
                                  )}
                                  {approval.profile_summary.website && (
                                    <div className="flex items-center">
                                      <Globe className="w-4 h-4 mr-2 text-gray-400" />
                                      <span className="text-gray-600">{language === 'ar' ? 'الموقع الإلكتروني:' : 'Website:'}</span>
                                      <a href={approval.profile_summary.website} target="_blank" rel="noopener noreferrer" className="ml-2 text-blue-600 hover:text-blue-800">
                                        {approval.profile_summary.website}
                                      </a>
                                    </div>
                                  )}
                                  {approval.profile_summary.linkedin_url && (
                                    <div className="flex items-center">
                                      <Globe className="w-4 h-4 mr-2 text-gray-400" />
                                      <span className="text-gray-600">{language === 'ar' ? 'لينكد إن:' : 'LinkedIn:'}</span>
                                      <a href={approval.profile_summary.linkedin_url} target="_blank" rel="noopener noreferrer" className="ml-2 text-blue-600 hover:text-blue-800">
                                        LinkedIn Profile
                                      </a>
                                    </div>
                                  )}
                                </div>
                                {approval.profile_summary.bio && (
                                  <div className="mt-3">
                                    <span className="text-gray-600 text-sm">{language === 'ar' ? 'النبذة:' : 'Bio:'}</span>
                                    <p className="mt-1 text-gray-900 text-sm">{approval.profile_summary.bio}</p>
                                  </div>
                                )}
                                {approval.profile_summary.created_at && (
                                  <div className="mt-3 pt-3 border-t border-gray-200">
                                    <span className="text-gray-600 text-sm">{language === 'ar' ? 'تاريخ إنشاء الملف الشخصي:' : 'Profile Created:'}</span>
                                    <p className="mt-1 text-gray-900 text-sm">
                                      {new Date(approval.profile_summary.created_at).toLocaleDateString(language === 'ar' ? 'ar-SA' : 'en-US', {
                                        year: 'numeric',
                                        month: 'long',
                                        day: 'numeric',
                                        hour: '2-digit',
                                        minute: '2-digit'
                                      })}
                                    </p>
                                  </div>
                                )}
                              </div>
                            )}

                            {/* Role-Specific Data */}
                            {approval.role_specific_data && approval.requested_role_info && (
                              <div>
                                <h4 className="text-sm font-medium text-gray-900 mb-2 flex items-center">
                                  {getRoleIcon(approval.requested_role_info.role_name)}
                                  <span className="ml-2">
                                    {language === 'ar' ? `معلومات ${approval.requested_role_info.role_display_name}` : `${approval.requested_role_info.role_display_name} Information`}
                                  </span>
                                </h4>

                                {approval.requested_role_info.role_name === 'entrepreneur' && (
                                  <div className="grid grid-cols-2 gap-4 text-sm">
                                    {approval.role_specific_data.business_name && (
                                      <div>
                                        <span className="text-gray-600">{language === 'ar' ? 'اسم المشروع:' : 'Business Name:'}</span>
                                        <span className="ml-2 text-gray-900">{approval.role_specific_data.business_name}</span>
                                      </div>
                                    )}
                                    {approval.role_specific_data.business_stage && (
                                      <div>
                                        <span className="text-gray-600">{language === 'ar' ? 'مرحلة المشروع:' : 'Business Stage:'}</span>
                                        <span className="ml-2 text-gray-900">{approval.role_specific_data.business_stage}</span>
                                      </div>
                                    )}
                                    {approval.role_specific_data.industry && (
                                      <div>
                                        <span className="text-gray-600">{language === 'ar' ? 'الصناعة:' : 'Industry:'}</span>
                                        <span className="ml-2 text-gray-900">{approval.role_specific_data.industry}</span>
                                      </div>
                                    )}
                                    {approval.role_specific_data.team_size && (
                                      <div>
                                        <span className="text-gray-600">{language === 'ar' ? 'حجم الفريق:' : 'Team Size:'}</span>
                                        <span className="ml-2 text-gray-900">{approval.role_specific_data.team_size}</span>
                                      </div>
                                    )}
                                    {approval.role_specific_data.funding_needed && (
                                      <div>
                                        <span className="text-gray-600">{language === 'ar' ? 'التمويل المطلوب:' : 'Funding Needed:'}</span>
                                        <span className="ml-2 text-gray-900">{approval.role_specific_data.funding_needed}</span>
                                      </div>
                                    )}
                                    {approval.role_specific_data.previous_experience && (
                                      <div>
                                        <span className="text-gray-600">{language === 'ar' ? 'الخبرة السابقة:' : 'Previous Experience:'}</span>
                                        <span className="ml-2 text-gray-900">{approval.role_specific_data.previous_experience}</span>
                                      </div>
                                    )}
                                    {approval.role_specific_data.business_description && (
                                      <div className="col-span-2">
                                        <span className="text-gray-600">{language === 'ar' ? 'وصف المشروع:' : 'Business Description:'}</span>
                                        <p className="mt-1 text-gray-900">{approval.role_specific_data.business_description}</p>
                                      </div>
                                    )}
                                    {approval.role_specific_data.support_needed && (
                                      <div className="col-span-2">
                                        <span className="text-gray-600">{language === 'ar' ? 'الدعم المطلوب:' : 'Support Needed:'}</span>
                                        <p className="mt-1 text-gray-900">{approval.role_specific_data.support_needed}</p>
                                      </div>
                                    )}
                                  </div>
                                )}

                                {approval.requested_role_info.role_name === 'mentor' && (
                                  <div className="grid grid-cols-2 gap-4 text-sm">
                                    {approval.role_specific_data.expertise && (
                                      <div>
                                        <span className="text-gray-600">{language === 'ar' ? 'مجال الخبرة:' : 'Expertise Area:'}</span>
                                        <span className="ml-2 text-gray-900">{approval.role_specific_data.expertise}</span>
                                      </div>
                                    )}
                                    {(approval.role_specific_data.mentor_experience || approval.role_specific_data.experience) && (
                                      <div>
                                        <span className="text-gray-600">{language === 'ar' ? 'سنوات الخبرة:' : 'Years of Experience:'}</span>
                                        <span className="ml-2 text-gray-900">{approval.role_specific_data.mentor_experience || approval.role_specific_data.experience}</span>
                                      </div>
                                    )}
                                    {approval.role_specific_data.availability && (
                                      <div>
                                        <span className="text-gray-600">{language === 'ar' ? 'التوفر:' : 'Availability:'}</span>
                                        <span className="ml-2 text-gray-900">{approval.role_specific_data.availability}</span>
                                      </div>
                                    )}
                                    {approval.role_specific_data.preferred_communication && (
                                      <div>
                                        <span className="text-gray-600">{language === 'ar' ? 'طريقة التواصل المفضلة:' : 'Preferred Communication:'}</span>
                                        <span className="ml-2 text-gray-900">{approval.role_specific_data.preferred_communication}</span>
                                      </div>
                                    )}
                                    {approval.role_specific_data.mentorship_areas && (
                                      <div className="col-span-2">
                                        <span className="text-gray-600">{language === 'ar' ? 'مجالات الإرشاد:' : 'Mentorship Areas:'}</span>
                                        <p className="mt-1 text-gray-900">{approval.role_specific_data.mentorship_areas}</p>
                                      </div>
                                    )}
                                  </div>
                                )}

                                {approval.requested_role_info.role_name === 'investor' && (
                                  <div className="grid grid-cols-2 gap-4 text-sm">
                                    {approval.role_specific_data.investment_range && (
                                      <div>
                                        <span className="text-gray-600">{language === 'ar' ? 'نطاق الاستثمار:' : 'Investment Range:'}</span>
                                        <span className="ml-2 text-gray-900">{approval.role_specific_data.investment_range}</span>
                                      </div>
                                    )}
                                    {approval.role_specific_data.investment_stage && (
                                      <div>
                                        <span className="text-gray-600">{language === 'ar' ? 'مرحلة الاستثمار:' : 'Investment Stage:'}</span>
                                        <span className="ml-2 text-gray-900">{approval.role_specific_data.investment_stage}</span>
                                      </div>
                                    )}
                                    {approval.role_specific_data.portfolio_companies && (
                                      <div className="col-span-2">
                                        <span className="text-gray-600">{language === 'ar' ? 'الشركات في المحفظة:' : 'Portfolio Companies:'}</span>
                                        <p className="mt-1 text-gray-900">{approval.role_specific_data.portfolio_companies}</p>
                                      </div>
                                    )}
                                    {approval.role_specific_data.preferred_industries && (
                                      <div className="col-span-2">
                                        <span className="text-gray-600">{language === 'ar' ? 'الصناعات المفضلة:' : 'Preferred Industries:'}</span>
                                        <p className="mt-1 text-gray-900">{approval.role_specific_data.preferred_industries}</p>
                                      </div>
                                    )}
                                    {approval.role_specific_data.investment_criteria && (
                                      <div className="col-span-2">
                                        <span className="text-gray-600">{language === 'ar' ? 'معايير الاستثمار:' : 'Investment Criteria:'}</span>
                                        <p className="mt-1 text-gray-900">{approval.role_specific_data.investment_criteria}</p>
                                      </div>
                                    )}
                                  </div>
                                )}

                                {/* User Role Information */}
                                {approval.requested_role_info.role_name === 'user' && (
                                  <div className="space-y-3 text-sm">
                                    {approval.role_specific_data.interests && (
                                      <div>
                                        <span className="text-gray-600">{language === 'ar' ? 'الاهتمامات:' : 'Interests:'}</span>
                                        <p className="mt-1 text-gray-900">{approval.role_specific_data.interests}</p>
                                      </div>
                                    )}
                                    {approval.role_specific_data.goals && (
                                      <div>
                                        <span className="text-gray-600">{language === 'ar' ? 'الأهداف:' : 'Goals:'}</span>
                                        <p className="mt-1 text-gray-900">{approval.role_specific_data.goals}</p>
                                      </div>
                                    )}
                                  </div>
                                )}
                              </div>
                            )}

                            {/* Application Motivation */}
                            {approval.requested_role_info?.motivation && (
                              <div>
                                <h4 className="text-sm font-medium text-gray-900 mb-2 flex items-center">
                                  <MessageSquare className="w-4 h-4 mr-2" />
                                  {language === 'ar' ? 'دافع التقديم' : 'Application Motivation'}
                                </h4>
                                <p className="text-sm text-gray-900 bg-white p-3 rounded border">
                                  {approval.requested_role_info.motivation}
                                </p>
                              </div>
                            )}

                            {/* Application Qualifications */}
                            {approval.requested_role_info?.qualifications && (
                              <div>
                                <h4 className="text-sm font-medium text-gray-900 mb-2 flex items-center">
                                  <Award className="w-4 h-4 mr-2" />
                                  {language === 'ar' ? 'المؤهلات' : 'Qualifications'}
                                </h4>
                                <p className="text-sm text-gray-900 bg-white p-3 rounded border">
                                  {approval.requested_role_info.qualifications}
                                </p>
                              </div>
                            )}

                            {/* Additional Application Information */}
                            {(approval.role_specific_data?.experience || approval.role_specific_data?.portfolio_url) && (
                              <div>
                                <h4 className="text-sm font-medium text-gray-900 mb-2 flex items-center">
                                  <Star className="w-4 h-4 mr-2" />
                                  {language === 'ar' ? 'معلومات إضافية' : 'Additional Information'}
                                </h4>
                                <div className="bg-white p-3 rounded border space-y-3">
                                  {approval.role_specific_data?.experience && (
                                    <div>
                                      <span className="text-gray-600 text-sm font-medium">{language === 'ar' ? 'الخبرة ذات الصلة:' : 'Relevant Experience:'}</span>
                                      <p className="mt-1 text-gray-900 text-sm">{approval.role_specific_data.experience}</p>
                                    </div>
                                  )}
                                  {approval.role_specific_data?.portfolio_url && (
                                    <div>
                                      <span className="text-gray-600 text-sm font-medium">{language === 'ar' ? 'رابط الأعمال:' : 'Portfolio URL:'}</span>
                                      <p className="mt-1">
                                        <a
                                          href={approval.role_specific_data.portfolio_url}
                                          target="_blank"
                                          rel="noopener noreferrer"
                                          className="text-blue-600 hover:text-blue-800 text-sm"
                                        >
                                          {approval.role_specific_data.portfolio_url}
                                        </a>
                                      </p>
                                    </div>
                                  )}
                                </div>
                              </div>
                            )}

                            {/* Application Metadata */}
                            <div>
                              <h4 className="text-sm font-medium text-gray-900 mb-2 flex items-center">
                                <Calendar className="w-4 h-4 mr-2" />
                                {language === 'ar' ? 'معلومات التقديم' : 'Application Information'}
                              </h4>
                              <div className="grid grid-cols-2 gap-4 text-sm bg-white p-3 rounded border">
                                {approval.requested_role_info?.applied_at && (
                                  <div>
                                    <span className="text-gray-600">{language === 'ar' ? 'تاريخ التقديم:' : 'Applied Date:'}</span>
                                    <p className="mt-1 text-gray-900">
                                      {new Date(approval.requested_role_info.applied_at).toLocaleDateString(language === 'ar' ? 'ar-SA' : 'en-US', {
                                        year: 'numeric',
                                        month: 'long',
                                        day: 'numeric',
                                        hour: '2-digit',
                                        minute: '2-digit'
                                      })}
                                    </p>
                                  </div>
                                )}
                                {approval.requested_role_info?.application_status && (
                                  <div>
                                    <span className="text-gray-600">{language === 'ar' ? 'حالة التقديم:' : 'Application Status:'}</span>
                                    <p className="mt-1 text-gray-900 capitalize">{approval.requested_role_info.application_status}</p>
                                  </div>
                                )}
                                {approval.days_pending !== null && (
                                  <div>
                                    <span className="text-gray-600">{language === 'ar' ? 'أيام الانتظار:' : 'Days Pending:'}</span>
                                    <p className="mt-1 text-gray-900">{approval.days_pending} {language === 'ar' ? 'يوم' : 'days'}</p>
                                  </div>
                                )}
                                <div>
                                  <span className="text-gray-600">{language === 'ar' ? 'الدور المطلوب:' : 'Requested Role:'}</span>
                                  <p className="mt-1 text-gray-900">{approval.requested_role_info?.role_display_name || approval.requested_role_info?.role_name}</p>
                                </div>
                              </div>
                            </div>
                          </div>
                        </td>
                      </tr>
                    )}
                  </React.Fragment>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Reject Modal */}
      {showRejectModal && selectedApproval && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                {language === 'ar' ? 'رفض طلب التسجيل' : 'Reject Registration Request'}
              </h3>
              
              <div className="mb-4">
                <p className="text-sm text-gray-600 mb-2">
                  {language === 'ar' ? 'المستخدم:' : 'User:'} {selectedApproval.user_full_name}
                </p>
                <p className="text-sm text-gray-600 mb-4">
                  {language === 'ar' ? 'البريد الإلكتروني:' : 'Email:'} {selectedApproval.user.email}
                </p>
              </div>

              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {language === 'ar' ? 'سبب الرفض:' : 'Rejection Reason:'}
                </label>
                <textarea
                  value={rejectionReason}
                  onChange={(e) => setRejectionReason(e.target.value)}
                  rows={4}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder={language === 'ar' 
                    ? 'اكتب سبب رفض الطلب. سيتم إرسال هذا السبب للمستخدم عبر البريد الإلكتروني.'
                    : 'Enter the reason for rejection. This will be sent to the user via email.'
                  }
                  dir={isRTL ? 'rtl' : 'ltr'}
                />
              </div>

              <div className="flex gap-3 justify-end">
                <button
                  onClick={() => {
                    setShowRejectModal(false);
                    setRejectionReason('');
                    setSelectedApproval(null);
                  }}
                  className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500"
                >
                  {language === 'ar' ? 'إلغاء' : 'Cancel'}
                </button>
                <button
                  onClick={handleReject}
                  disabled={!rejectionReason.trim()}
                  className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {language === 'ar' ? 'رفض الطلب' : 'Reject Request'}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default UserApprovalManager;