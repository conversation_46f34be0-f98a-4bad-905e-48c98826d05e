/**
 * ✅ AI-FOCUSED LAYOUT
 * Specialized layout for AI chat pages with optimized sidebar
 */

import React, { useState } from 'react';
import { useLanguage } from '../../hooks/useLanguage';
import UniversalSidebar from './UniversalSidebar';
import { Menu } from 'lucide-react';

interface AIFocusedLayoutProps {
  children: React.ReactNode;
}

const AIFocusedLayout: React.FC<AIFocusedLayoutProps> = ({ children }) => {
  const { isRTL } = useLanguage();
  const [isMobileSidebarOpen, setIsMobileSidebarOpen] = useState(false);
  const [isDesktopSidebarCollapsed, setIsDesktopSidebarCollapsed] = useState(false);

  return (
    <div className={`min-h-screen flex ${isRTL ? "flex-row-reverse font-arabic" : ""}`} dir={isRTL ? "rtl" : "ltr"}>
      {/* Universal Sidebar for authenticated users - OPTIMIZED */}
      <>
        {/* Desktop Sidebar - Only visible on large screens */}
        <div className="hidden lg:block">
          <UniversalSidebar
            variant="desktop"
            isCollapsed={isDesktopSidebarCollapsed}
            onToggle={() => setIsDesktopSidebarCollapsed(!isDesktopSidebarCollapsed)}
          />
        </div>

        {/* Mobile Sidebar - Only render when needed */}
        {isMobileSidebarOpen && (
          <UniversalSidebar
            variant="mobile"
            isOpen={isMobileSidebarOpen}
            onClose={() => setIsMobileSidebarOpen(false)}
          />
        )}
      </>

      {/* Main Content Area - IMPROVED FOR AI CHAT */}
      <div className="flex-1 flex flex-col min-h-screen relative transition-all duration-300 ease-in-out">
        {/* Mobile Menu Button - Consistent with AuthenticatedLayout */}
        <button
          onClick={() => setIsMobileSidebarOpen(true)}
          className={`lg:hidden fixed top-4 ${isRTL ? 'right-4' : 'left-4'} z-50 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 p-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105`}
          aria-label="Open Menu"
          title="Open Menu"
        >
          <Menu className="w-5 h-5 text-white" />
        </button>

        {/* AI Chat Content - Full height for chat interface */}
        <div className="flex-1 flex flex-col transition-all duration-300 ease-in-out min-h-0">
          {children}
        </div>
      </div>
    </div>
  );
};

export default AIFocusedLayout;
